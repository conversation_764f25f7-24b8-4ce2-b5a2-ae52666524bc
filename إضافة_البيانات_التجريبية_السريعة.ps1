﻿$dbPath = "نظام_إدارة_تصنيع_المعمول.accdb"
try {
    $dao = New-Object -ComObject DAO.DBEngine.120
    $db = $dao.OpenDatabase($dbPath)
    
    # إضافة وصفة معمول الجوز والعسل
    $rs = $db.OpenRecordset("الوصفات")
    $rs.AddNew()
    $rs.Fields("رقم_المنتج").Value = 10
    $rs.Fields("اسم_الوصفة").Value = "وصفة معمول الجوز والعسل - 100 قطعة"
    $rs.Fields("كمية_الإنتاج").Value = 100
    $rs.Fields("وحدة_الإنتاج").Value = 3
    $rs.Update()
    $rs.Close()
    
    # إضافة مكونات الوصفة
    $rs = $db.OpenRecordset("مكونات_الوصفة")
    $components = @(
        @{Recipe=1; Material=1; Qty=8.0; Unit=1; Cost=180.00; Waste=2.0},
        @{Recipe=1; Material=2; Qty=15.0; Unit=1; Cost=12.50; Waste=1.0},
        @{Recipe=1; Material=3; Qty=3.5; Unit=1; Cost=85.00; Waste=0.5},
        @{Recipe=1; Material=4; Qty=2.0; Unit=1; Cost=45.00; Waste=1.0},
        @{Recipe=1; Material=5; Qty=1.5; Unit=1; Cost=18.00; Waste=0.0},
        @{Recipe=1; Material=6; Qty=0.05; Unit=1; Cost=120.00; Waste=0.0},
        @{Recipe=1; Material=7; Qty=0.1; Unit=5; Cost=25.00; Waste=0.0},
        @{Recipe=1; Material=8; Qty=100.0; Unit=3; Cost=2.50; Waste=0.0},
        @{Recipe=1; Material=9; Qty=100.0; Unit=3; Cost=0.15; Waste=0.0}
    )
    
    foreach ($comp in $components) {
        $rs.AddNew()
        $rs.Fields("رقم_الوصفة").Value = $comp.Recipe
        $rs.Fields("رقم_المادة").Value = $comp.Material
        $rs.Fields("الكمية_المطلوبة").Value = $comp.Qty
        $rs.Fields("وحدة_القياس").Value = $comp.Unit
        $rs.Fields("التكلفة_المعيارية").Value = $comp.Cost
        $rs.Fields("نسبة_الفاقد").Value = $comp.Waste
        $rs.Update()
    }
    $rs.Close()
    
    # إضافة أرصدة المخزون الافتتاحية
    $rs = $db.OpenRecordset("أرصدة_المخزون")
    $inventory = @(
        @{Material=1; Warehouse=1; Batch="NUT-2024-001"; Qty=200.0; Cost=180.00},
        @{Material=2; Warehouse=1; Batch="FLR-2024-001"; Qty=500.0; Cost=12.50},
        @{Material=3; Warehouse=1; Batch="HON-2024-001"; Qty=100.0; Cost=85.00},
        @{Material=4; Warehouse=1; Batch="BUT-2024-001"; Qty=150.0; Cost=45.00},
        @{Material=5; Warehouse=1; Batch="SUG-2024-001"; Qty=300.0; Cost=18.00},
        @{Material=6; Warehouse=1; Batch="CIN-2024-001"; Qty=25.0; Cost=120.00},
        @{Material=7; Warehouse=1; Batch="VAN-2024-001"; Qty=50.0; Cost=25.00},
        @{Material=8; Warehouse=3; Batch="BOX-2024-001"; Qty=2000.0; Cost=2.50},
        @{Material=9; Warehouse=3; Batch="LAB-2024-001"; Qty=5000.0; Cost=0.15}
    )
    
    foreach ($inv in $inventory) {
        $rs.AddNew()
        $rs.Fields("رقم_المادة").Value = $inv.Material
        $rs.Fields("رقم_المستودع").Value = $inv.Warehouse
        $rs.Fields("رقم_الدفعة").Value = $inv.Batch
        $rs.Fields("الكمية_المتاحة").Value = $inv.Qty
        $rs.Fields("التكلفة_الوحدة").Value = $inv.Cost
        $rs.Update()
    }
    $rs.Close()
    
    Write-Host "تم إضافة البيانات التجريبية بنجاح" -ForegroundColor Green
    
} catch {
    Write-Host "خطأ في إضافة البيانات التجريبية: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($rs) { $rs.Close() }
    if ($db) { $db.Close() }
    if ($dao) { [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dao) | Out-Null }
}
