# سكريبت إنشاء نظام إدارة تصنيع المعمول البسيط والصحيح
# Simple and Correct Ma'amoul Manufacturing Management System

Write-Host "===============================================" -ForegroundColor Magenta
Write-Host "    إنشاء نظام إدارة تصنيع المعمول الصحيح     " -ForegroundColor Magenta
Write-Host "===============================================" -ForegroundColor Magenta
Write-Host ""

# إغلاق أي عمليات Access مفتوحة
Write-Host "إغلاق أي عمليات Access مفتوحة..." -ForegroundColor Yellow
Get-Process -Name "MSACCESS" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 3

# حذف قاعدة البيانات الموجودة
$dbPath = "نظام_إدارة_تصنيع_المعمول_جديد.accdb"
if (Test-Path $dbPath) {
    Remove-Item $dbPath -Force -ErrorAction SilentlyContinue
    Write-Host "تم حذف قاعدة البيانات الموجودة" -ForegroundColor Yellow
}

try {
    Write-Host "إنشاء قاعدة البيانات الجديدة..." -ForegroundColor Green
    
    # إنشاء كائن DAO
    $dao = New-Object -ComObject DAO.DBEngine.120
    
    # إنشاء قاعدة البيانات مع دعم اللغة العربية
    $db = $dao.CreateDatabase($dbPath, ";LANGID=0x0401;CP=1256;COUNTRY=0")
    
    Write-Host "تم إنشاء قاعدة البيانات بنجاح" -ForegroundColor Green
    
    # إنشاء الجداول باستخدام SQL بسيط
    Write-Host "إنشاء الجداول..." -ForegroundColor Cyan
    
    # 1. جدول وحدات القياس
    $db.Execute("CREATE TABLE وحدات_القياس (رقم_الوحدة AUTOINCREMENT PRIMARY KEY, اسم_الوحدة TEXT(50), الرمز TEXT(10), نوع_الوحدة TEXT(20))")
    Write-Host "تم إنشاء جدول وحدات القياس" -ForegroundColor Green
    
    # 2. جدول المستودعات
    $db.Execute("CREATE TABLE المستودعات (رقم_المستودع AUTOINCREMENT PRIMARY KEY, اسم_المستودع TEXT(100), الموقع TEXT(200), المسؤول TEXT(100))")
    Write-Host "تم إنشاء جدول المستودعات" -ForegroundColor Green
    
    # 3. جدول فئات المواد
    $db.Execute("CREATE TABLE فئات_المواد (رقم_الفئة AUTOINCREMENT PRIMARY KEY, اسم_الفئة TEXT(100), الوصف MEMO)")
    Write-Host "تم إنشاء جدول فئات المواد" -ForegroundColor Green
    
    # 4. جدول الموردين
    $db.Execute("CREATE TABLE الموردين (رقم_المورد AUTOINCREMENT PRIMARY KEY, اسم_المورد TEXT(200), العنوان MEMO, الهاتف TEXT(20), البريد_الإلكتروني TEXT(100))")
    Write-Host "تم إنشاء جدول الموردين" -ForegroundColor Green
    
    # 5. جدول العملاء
    $db.Execute("CREATE TABLE العملاء (رقم_العميل AUTOINCREMENT PRIMARY KEY, اسم_العميل TEXT(200), العنوان MEMO, الهاتف TEXT(20), حد_الائتمان CURRENCY)")
    Write-Host "تم إنشاء جدول العملاء" -ForegroundColor Green
    
    # 6. جدول المواد والمنتجات
    $db.Execute("CREATE TABLE المواد_والمنتجات (رقم_المادة AUTOINCREMENT PRIMARY KEY, كود_المادة TEXT(50), اسم_المادة TEXT(200), رقم_الفئة LONG, وحدة_القياس_الأساسية LONG, التكلفة_المعيارية CURRENCY, سعر_البيع CURRENCY, الحد_الأدنى_للمخزون DOUBLE, نوع_المادة TEXT(20))")
    Write-Host "تم إنشاء جدول المواد والمنتجات" -ForegroundColor Green
    
    # 7. جدول أرصدة المخزون
    $db.Execute("CREATE TABLE أرصدة_المخزون (رقم_الرصيد AUTOINCREMENT PRIMARY KEY, رقم_المادة LONG, رقم_المستودع LONG, رقم_الدفعة TEXT(50), الكمية_المتاحة DOUBLE, التكلفة_الوحدة CURRENCY)")
    Write-Host "تم إنشاء جدول أرصدة المخزون" -ForegroundColor Green
    
    # 8. جدول حركات المخزون
    $db.Execute("CREATE TABLE حركات_المخزون (رقم_الحركة AUTOINCREMENT PRIMARY KEY, رقم_المادة LONG, رقم_المستودع LONG, نوع_الحركة TEXT(20), تاريخ_الحركة DATETIME, الكمية DOUBLE, التكلفة_الوحدة CURRENCY, المستخدم TEXT(50))")
    Write-Host "تم إنشاء جدول حركات المخزون" -ForegroundColor Green
    
    # 9. جدول الوصفات
    $db.Execute("CREATE TABLE الوصفات (رقم_الوصفة AUTOINCREMENT PRIMARY KEY, رقم_المنتج LONG, اسم_الوصفة TEXT(200), كمية_الإنتاج DOUBLE, وحدة_الإنتاج LONG)")
    Write-Host "تم إنشاء جدول الوصفات" -ForegroundColor Green
    
    # 10. جدول مكونات الوصفة
    $db.Execute("CREATE TABLE مكونات_الوصفة (رقم_المكون AUTOINCREMENT PRIMARY KEY, رقم_الوصفة LONG, رقم_المادة LONG, الكمية_المطلوبة DOUBLE, وحدة_القياس LONG, التكلفة_المعيارية CURRENCY)")
    Write-Host "تم إنشاء جدول مكونات الوصفة" -ForegroundColor Green
    
    # إدراج البيانات الأساسية
    Write-Host "إدراج البيانات الأساسية..." -ForegroundColor Cyan
    
    # وحدات القياس
    $db.Execute("INSERT INTO وحدات_القياس (اسم_الوحدة, الرمز, نوع_الوحدة) VALUES ('كيلوجرام', 'كجم', 'وزن')")
    $db.Execute("INSERT INTO وحدات_القياس (اسم_الوحدة, الرمز, نوع_الوحدة) VALUES ('جرام', 'جم', 'وزن')")
    $db.Execute("INSERT INTO وحدات_القياس (اسم_الوحدة, الرمز, نوع_الوحدة) VALUES ('قطعة', 'قطعة', 'عدد')")
    $db.Execute("INSERT INTO وحدات_القياس (اسم_الوحدة, الرمز, نوع_الوحدة) VALUES ('علبة', 'علبة', 'تعبئة')")
    $db.Execute("INSERT INTO وحدات_القياس (اسم_الوحدة, الرمز, نوع_الوحدة) VALUES ('لتر', 'لتر', 'حجم')")
    Write-Host "تم إدراج وحدات القياس" -ForegroundColor Green
    
    # المستودعات
    $db.Execute("INSERT INTO المستودعات (اسم_المستودع, الموقع, المسؤول) VALUES ('مستودع المواد الخام', 'الطابق الأرضي - قسم أ', 'أحمد محمد')")
    $db.Execute("INSERT INTO المستودعات (اسم_المستودع, الموقع, المسؤول) VALUES ('مستودع المنتجات النهائية', 'الطابق الأول - قسم ب', 'فاطمة علي')")
    $db.Execute("INSERT INTO المستودعات (اسم_المستودع, الموقع, المسؤول) VALUES ('مستودع التعبئة والتغليف', 'الطابق الأرضي - قسم ج', 'محمد حسن')")
    Write-Host "تم إدراج المستودعات" -ForegroundColor Green
    
    # فئات المواد
    $db.Execute("INSERT INTO فئات_المواد (اسم_الفئة, الوصف) VALUES ('مكسرات', 'جميع أنواع المكسرات المستخدمة في الإنتاج')")
    $db.Execute("INSERT INTO فئات_المواد (اسم_الفئة, الوصف) VALUES ('دقيق ونشويات', 'الدقيق والنشا والمواد النشوية')")
    $db.Execute("INSERT INTO فئات_المواد (اسم_الفئة, الوصف) VALUES ('سكريات ومحليات', 'السكر والعسل والمحليات الطبيعية')")
    $db.Execute("INSERT INTO فئات_المواد (اسم_الفئة, الوصف) VALUES ('زيوت ودهون', 'الزيوت والزبدة والدهون المستخدمة')")
    $db.Execute("INSERT INTO فئات_المواد (اسم_الفئة, الوصف) VALUES ('توابل ونكهات', 'التوابل والنكهات والمواد المحسنة')")
    $db.Execute("INSERT INTO فئات_المواد (اسم_الفئة, الوصف) VALUES ('مواد تعبئة وتغليف', 'العلب والأكياس ومواد التغليف')")
    $db.Execute("INSERT INTO فئات_المواد (اسم_الفئة, الوصف) VALUES ('منتجات نهائية', 'المنتجات النهائية الجاهزة للبيع')")
    Write-Host "تم إدراج فئات المواد" -ForegroundColor Green
    
    # الموردين
    $db.Execute("INSERT INTO الموردين (اسم_المورد, العنوان, الهاتف, البريد_الإلكتروني) VALUES ('شركة المكسرات المصرية', 'القاهرة - مدينة نصر', '01234567890', '<EMAIL>')")
    $db.Execute("INSERT INTO الموردين (اسم_المورد, العنوان, الهاتف, البريد_الإلكتروني) VALUES ('مطاحن الدقيق الحديثة', 'الجيزة - 6 أكتوبر', '01098765432', '<EMAIL>')")
    $db.Execute("INSERT INTO الموردين (اسم_المورد, العنوان, الهاتف, البريد_الإلكتروني) VALUES ('مناحل العسل الطبيعي', 'الفيوم - سنورس', '01156789012', '<EMAIL>')")
    Write-Host "تم إدراج الموردين" -ForegroundColor Green
    
    # العملاء
    $db.Execute("INSERT INTO العملاء (اسم_العميل, العنوان, الهاتف, حد_الائتمان) VALUES ('سوبر ماركت الأهرام', 'القاهرة - مصر الجديدة', '0227654321', 50000)")
    $db.Execute("INSERT INTO العملاء (اسم_العميل, العنوان, الهاتف, حد_الائتمان) VALUES ('هايبر ماركت كارفور', 'الجيزة - المهندسين', '0233456789', 100000)")
    $db.Execute("INSERT INTO العملاء (اسم_العميل, العنوان, الهاتف, حد_الائتمان) VALUES ('متاجر سبينيس', 'القاهرة - التجمع الخامس', '0226789012', 75000)")
    Write-Host "تم إدراج العملاء" -ForegroundColor Green
    
    # المواد والمنتجات
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, نوع_المادة) VALUES ('NUT001', 'جوز مقشر درجة أولى', 1, 1, 180.00, 50, 'مادة خام')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, نوع_المادة) VALUES ('FLR001', 'دقيق أبيض فاخر', 2, 1, 12.50, 100, 'مادة خام')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, نوع_المادة) VALUES ('HON001', 'عسل نحل طبيعي', 3, 1, 85.00, 20, 'مادة خام')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, نوع_المادة) VALUES ('BUT001', 'زبدة طبيعية', 4, 1, 45.00, 30, 'مادة خام')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, نوع_المادة) VALUES ('SUG001', 'سكر أبيض ناعم', 3, 1, 18.00, 50, 'مادة خام')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, نوع_المادة) VALUES ('CIN001', 'قرفة مطحونة', 5, 2, 120.00, 5, 'مادة خام')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, نوع_المادة) VALUES ('VAN001', 'فانيليا سائلة', 5, 5, 25.00, 10, 'مادة خام')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, نوع_المادة) VALUES ('BOX001', 'علبة كرتون 600 جرام', 6, 3, 2.50, 500, 'مادة تعبئة')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, نوع_المادة) VALUES ('LAB001', 'ملصق المنتج', 6, 3, 0.15, 1000, 'مادة تعبئة')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, سعر_البيع, الحد_الأدنى_للمخزون, نوع_المادة) VALUES ('PRD001', 'معمول الجوز والعسل 600 جرام', 7, 4, 24.65, 85.00, 50, 'منتج نهائي')")
    Write-Host "تم إدراج المواد والمنتجات" -ForegroundColor Green
    
    # وصفة معمول الجوز والعسل
    $db.Execute("INSERT INTO الوصفات (رقم_المنتج, اسم_الوصفة, كمية_الإنتاج, وحدة_الإنتاج) VALUES (10, 'وصفة معمول الجوز والعسل - 100 قطعة', 100, 3)")
    Write-Host "تم إدراج الوصفة" -ForegroundColor Green
    
    # مكونات الوصفة
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية) VALUES (1, 1, 8.0, 1, 180.00)")
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية) VALUES (1, 2, 15.0, 1, 12.50)")
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية) VALUES (1, 3, 3.5, 1, 85.00)")
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية) VALUES (1, 4, 2.0, 1, 45.00)")
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية) VALUES (1, 5, 1.5, 1, 18.00)")
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية) VALUES (1, 6, 0.05, 1, 120.00)")
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية) VALUES (1, 7, 0.1, 5, 25.00)")
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية) VALUES (1, 8, 100.0, 3, 2.50)")
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية) VALUES (1, 9, 100.0, 3, 0.15)")
    Write-Host "تم إدراج مكونات الوصفة" -ForegroundColor Green
    
    # أرصدة المخزون الافتتاحية
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, الكمية_المتاحة, التكلفة_الوحدة) VALUES (1, 1, 'NUT-2024-001', 200.0, 180.00)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, الكمية_المتاحة, التكلفة_الوحدة) VALUES (2, 1, 'FLR-2024-001', 500.0, 12.50)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, الكمية_المتاحة, التكلفة_الوحدة) VALUES (3, 1, 'HON-2024-001', 100.0, 85.00)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, الكمية_المتاحة, التكلفة_الوحدة) VALUES (4, 1, 'BUT-2024-001', 150.0, 45.00)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, الكمية_المتاحة, التكلفة_الوحدة) VALUES (5, 1, 'SUG-2024-001', 300.0, 18.00)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, الكمية_المتاحة, التكلفة_الوحدة) VALUES (6, 1, 'CIN-2024-001', 25.0, 120.00)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, الكمية_المتاحة, التكلفة_الوحدة) VALUES (7, 1, 'VAN-2024-001', 50.0, 25.00)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, الكمية_المتاحة, التكلفة_الوحدة) VALUES (8, 3, 'BOX-2024-001', 2000.0, 2.50)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, الكمية_المتاحة, التكلفة_الوحدة) VALUES (9, 3, 'LAB-2024-001', 5000.0, 0.15)")
    Write-Host "تم إدراج أرصدة المخزون الافتتاحية" -ForegroundColor Green
    
    # إغلاق قاعدة البيانات
    $db.Close()
    
    Write-Host ""
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host "          تم إنجاز النظام بنجاح كامل!          " -ForegroundColor Green
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "📊 ملخص النظام المكتمل:" -ForegroundColor Cyan
    Write-Host "✓ 10 جداول أساسية مع البيانات" -ForegroundColor Green
    Write-Host "✓ وحدات القياس (5 وحدات)" -ForegroundColor Green
    Write-Host "✓ المستودعات (3 مستودعات)" -ForegroundColor Green
    Write-Host "✓ فئات المواد (7 فئات)" -ForegroundColor Green
    Write-Host "✓ الموردين (3 موردين)" -ForegroundColor Green
    Write-Host "✓ العملاء (3 عملاء)" -ForegroundColor Green
    Write-Host "✓ المواد والمنتجات (10 مواد)" -ForegroundColor Green
    Write-Host "✓ وصفة معمول الجوز والعسل مع 9 مكونات" -ForegroundColor Green
    Write-Host "✓ أرصدة مخزون افتتاحية لجميع المواد" -ForegroundColor Green
    Write-Host "✓ واجهة عربية كاملة مع ترميز صحيح" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "📁 ملف قاعدة البيانات: $dbPath" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "🚀 للبدء في الاستخدام:" -ForegroundColor Cyan
    Write-Host "1. افتح ملف: $dbPath" -ForegroundColor White
    Write-Host "2. استعرض الجداول والبيانات المدرجة" -ForegroundColor White
    Write-Host "3. يمكنك إنشاء الاستعلامات والنماذج والتقارير يدوياً" -ForegroundColor White
    Write-Host "4. راجع وصفة معمول الجوز والعسل في جدول الوصفات" -ForegroundColor White
    Write-Host "5. تحقق من أرصدة المخزون في جدول أرصدة المخزون" -ForegroundColor White
    Write-Host ""
    
    # فتح قاعدة البيانات
    Write-Host "فتح قاعدة البيانات..." -ForegroundColor Green
    Start-Process $dbPath
    Write-Host "✓ تم فتح قاعدة البيانات في Microsoft Access" -ForegroundColor Green
    
} catch {
    Write-Host "خطأ في إنشاء النظام: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # تنظيف الكائنات
    if ($db) { $db.Close() }
    if ($dao) { [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dao) | Out-Null }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}

Write-Host ""
Write-Host "===============================================" -ForegroundColor Magenta
Write-Host "       النظام جاهز للاستخدام الفوري!         " -ForegroundColor Magenta
Write-Host "===============================================" -ForegroundColor Magenta
