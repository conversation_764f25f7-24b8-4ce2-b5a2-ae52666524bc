# سكريبت إنشاء نظام إدارة تصنيع المعمول المحسن والصحيح
# Improved and Correct Ma'amoul Manufacturing Management System

Write-Host "بدء إنشاء نظام إدارة تصنيع المعمول المحسن..." -ForegroundColor Green

# حذف قاعدة البيانات الموجودة إن وجدت
$dbPath = "نظام_إدارة_تصنيع_المعمول.accdb"
if (Test-Path $dbPath) {
    Remove-Item $dbPath -Force
    Write-Host "تم حذف قاعدة البيانات الموجودة" -ForegroundColor Yellow
}

try {
    # إنشاء كائن DAO
    $dao = New-Object -ComObject DAO.DBEngine.120
    
    # إنشاء قاعدة البيانات مع دعم اللغة العربية
    $db = $dao.CreateDatabase($dbPath, ";LANGID=0x0401;CP=1256;COUNTRY=0")
    
    Write-Host "تم إنشاء قاعدة البيانات بنجاح" -ForegroundColor Green
    
    # إنشاء الجداول باستخدام DAO بدلاً من SQL
    Write-Host "إنشاء الجداول..." -ForegroundColor Cyan
    
    # 1. جدول وحدات القياس
    $tdf = $db.CreateTableDef("وحدات_القياس")
    $tdf.Fields.Append($tdf.CreateField("رقم_الوحدة", 4))  # dbLong
    $tdf.Fields("رقم_الوحدة").Attributes = 17  # dbAutoIncrField
    $tdf.Fields.Append($tdf.CreateField("اسم_الوحدة", 10, 50))  # dbText
    $tdf.Fields.Append($tdf.CreateField("الرمز", 10, 10))
    $tdf.Fields.Append($tdf.CreateField("نوع_الوحدة", 10, 20))
    $tdf.Fields.Append($tdf.CreateField("معامل_التحويل", 7))  # dbDouble
    $tdf.Fields("معامل_التحويل").DefaultValue = "1"
    $tdf.Fields.Append($tdf.CreateField("ملاحظات", 12))  # dbMemo
    
    # إنشاء المفتاح الأساسي
    $idx = $tdf.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idx.Fields.Append($idx.CreateField("رقم_الوحدة"))
    $tdf.Indexes.Append($idx)
    
    $db.TableDefs.Append($tdf)
    Write-Host "تم إنشاء جدول وحدات القياس" -ForegroundColor Green
    
    # 2. جدول المستودعات
    $tdf = $db.CreateTableDef("المستودعات")
    $tdf.Fields.Append($tdf.CreateField("رقم_المستودع", 4))
    $tdf.Fields("رقم_المستودع").Attributes = 17
    $tdf.Fields.Append($tdf.CreateField("اسم_المستودع", 10, 100))
    $tdf.Fields.Append($tdf.CreateField("الموقع", 10, 200))
    $tdf.Fields.Append($tdf.CreateField("المسؤول", 10, 100))
    $tdf.Fields.Append($tdf.CreateField("الهاتف", 10, 20))
    $tdf.Fields.Append($tdf.CreateField("نشط", 1))  # dbBoolean
    $tdf.Fields("نشط").DefaultValue = "True"
    $tdf.Fields.Append($tdf.CreateField("ملاحظات", 12))
    
    $idx = $tdf.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idx.Fields.Append($idx.CreateField("رقم_المستودع"))
    $tdf.Indexes.Append($idx)
    
    $db.TableDefs.Append($tdf)
    Write-Host "تم إنشاء جدول المستودعات" -ForegroundColor Green
    
    # 3. جدول فئات المواد
    $tdf = $db.CreateTableDef("فئات_المواد")
    $tdf.Fields.Append($tdf.CreateField("رقم_الفئة", 4))
    $tdf.Fields("رقم_الفئة").Attributes = 17
    $tdf.Fields.Append($tdf.CreateField("اسم_الفئة", 10, 100))
    $tdf.Fields.Append($tdf.CreateField("الوصف", 12))
    $tdf.Fields.Append($tdf.CreateField("نشط", 1))
    $tdf.Fields("نشط").DefaultValue = "True"
    
    $idx = $tdf.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idx.Fields.Append($idx.CreateField("رقم_الفئة"))
    $tdf.Indexes.Append($idx)
    
    $db.TableDefs.Append($tdf)
    Write-Host "تم إنشاء جدول فئات المواد" -ForegroundColor Green
    
    # 4. جدول الموردين
    $tdf = $db.CreateTableDef("الموردين")
    $tdf.Fields.Append($tdf.CreateField("رقم_المورد", 4))
    $tdf.Fields("رقم_المورد").Attributes = 17
    $tdf.Fields.Append($tdf.CreateField("اسم_المورد", 10, 200))
    $tdf.Fields.Append($tdf.CreateField("العنوان", 12))
    $tdf.Fields.Append($tdf.CreateField("الهاتف", 10, 20))
    $tdf.Fields.Append($tdf.CreateField("البريد_الإلكتروني", 10, 100))
    $tdf.Fields.Append($tdf.CreateField("شخص_الاتصال", 10, 100))
    $tdf.Fields.Append($tdf.CreateField("شروط_الدفع", 10, 100))
    $tdf.Fields.Append($tdf.CreateField("نشط", 1))
    $tdf.Fields("نشط").DefaultValue = "True"
    $tdf.Fields.Append($tdf.CreateField("ملاحظات", 12))
    
    $idx = $tdf.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idx.Fields.Append($idx.CreateField("رقم_المورد"))
    $tdf.Indexes.Append($idx)
    
    $db.TableDefs.Append($tdf)
    Write-Host "تم إنشاء جدول الموردين" -ForegroundColor Green
    
    # 5. جدول العملاء
    $tdf = $db.CreateTableDef("العملاء")
    $tdf.Fields.Append($tdf.CreateField("رقم_العميل", 4))
    $tdf.Fields("رقم_العميل").Attributes = 17
    $tdf.Fields.Append($tdf.CreateField("اسم_العميل", 10, 200))
    $tdf.Fields.Append($tdf.CreateField("العنوان", 12))
    $tdf.Fields.Append($tdf.CreateField("الهاتف", 10, 20))
    $tdf.Fields.Append($tdf.CreateField("البريد_الإلكتروني", 10, 100))
    $tdf.Fields.Append($tdf.CreateField("شخص_الاتصال", 10, 100))
    $tdf.Fields.Append($tdf.CreateField("حد_الائتمان", 6))  # dbCurrency
    $tdf.Fields("حد_الائتمان").DefaultValue = "0"
    $tdf.Fields.Append($tdf.CreateField("نشط", 1))
    $tdf.Fields("نشط").DefaultValue = "True"
    $tdf.Fields.Append($tdf.CreateField("ملاحظات", 12))
    
    $idx = $tdf.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idx.Fields.Append($idx.CreateField("رقم_العميل"))
    $tdf.Indexes.Append($idx)
    
    $db.TableDefs.Append($tdf)
    Write-Host "تم إنشاء جدول العملاء" -ForegroundColor Green
    
    # 6. جدول المواد والمنتجات
    $tdf = $db.CreateTableDef("المواد_والمنتجات")
    $tdf.Fields.Append($tdf.CreateField("رقم_المادة", 4))
    $tdf.Fields("رقم_المادة").Attributes = 17
    $tdf.Fields.Append($tdf.CreateField("كود_المادة", 10, 50))
    $tdf.Fields.Append($tdf.CreateField("اسم_المادة", 10, 200))
    $tdf.Fields.Append($tdf.CreateField("رقم_الفئة", 4))
    $tdf.Fields.Append($tdf.CreateField("وحدة_القياس_الأساسية", 4))
    $tdf.Fields.Append($tdf.CreateField("التكلفة_المعيارية", 6))
    $tdf.Fields("التكلفة_المعيارية").DefaultValue = "0"
    $tdf.Fields.Append($tdf.CreateField("سعر_البيع", 6))
    $tdf.Fields("سعر_البيع").DefaultValue = "0"
    $tdf.Fields.Append($tdf.CreateField("الحد_الأدنى_للمخزون", 7))
    $tdf.Fields("الحد_الأدنى_للمخزون").DefaultValue = "0"
    $tdf.Fields.Append($tdf.CreateField("الحد_الأقصى_للمخزون", 7))
    $tdf.Fields("الحد_الأقصى_للمخزون").DefaultValue = "0"
    $tdf.Fields.Append($tdf.CreateField("نقطة_إعادة_الطلب", 7))
    $tdf.Fields("نقطة_إعادة_الطلب").DefaultValue = "0"
    $tdf.Fields.Append($tdf.CreateField("مدة_الصلاحية_بالأيام", 4))
    $tdf.Fields("مدة_الصلاحية_بالأيام").DefaultValue = "0"
    $tdf.Fields.Append($tdf.CreateField("نوع_المادة", 10, 20))
    $tdf.Fields("نوع_المادة").DefaultValue = "'مادة خام'"
    $tdf.Fields.Append($tdf.CreateField("نشط", 1))
    $tdf.Fields("نشط").DefaultValue = "True"
    $tdf.Fields.Append($tdf.CreateField("ملاحظات", 12))
    
    $idx = $tdf.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idx.Fields.Append($idx.CreateField("رقم_المادة"))
    $tdf.Indexes.Append($idx)
    
    $db.TableDefs.Append($tdf)
    Write-Host "تم إنشاء جدول المواد والمنتجات" -ForegroundColor Green
    
    # 7. جدول أرصدة المخزون
    $tdf = $db.CreateTableDef("أرصدة_المخزون")
    $tdf.Fields.Append($tdf.CreateField("رقم_الرصيد", 4))
    $tdf.Fields("رقم_الرصيد").Attributes = 17
    $tdf.Fields.Append($tdf.CreateField("رقم_المادة", 4))
    $tdf.Fields.Append($tdf.CreateField("رقم_المستودع", 4))
    $tdf.Fields.Append($tdf.CreateField("رقم_الدفعة", 10, 50))
    $tdf.Fields.Append($tdf.CreateField("تاريخ_الإنتاج", 8))  # dbDate
    $tdf.Fields.Append($tdf.CreateField("تاريخ_انتهاء_الصلاحية", 8))
    $tdf.Fields.Append($tdf.CreateField("الكمية_المتاحة", 7))
    $tdf.Fields("الكمية_المتاحة").DefaultValue = "0"
    $tdf.Fields.Append($tdf.CreateField("التكلفة_الوحدة", 6))
    $tdf.Fields("التكلفة_الوحدة").DefaultValue = "0"
    $tdf.Fields.Append($tdf.CreateField("تاريخ_آخر_تحديث", 8))
    $tdf.Fields("تاريخ_آخر_تحديث").DefaultValue = "Now()"
    
    $idx = $tdf.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idx.Fields.Append($idx.CreateField("رقم_الرصيد"))
    $tdf.Indexes.Append($idx)
    
    $db.TableDefs.Append($tdf)
    Write-Host "تم إنشاء جدول أرصدة المخزون" -ForegroundColor Green
    
    # 8. جدول حركات المخزون
    $tdf = $db.CreateTableDef("حركات_المخزون")
    $tdf.Fields.Append($tdf.CreateField("رقم_الحركة", 4))
    $tdf.Fields("رقم_الحركة").Attributes = 17
    $tdf.Fields.Append($tdf.CreateField("رقم_المادة", 4))
    $tdf.Fields.Append($tdf.CreateField("رقم_المستودع", 4))
    $tdf.Fields.Append($tdf.CreateField("نوع_الحركة", 10, 20))
    $tdf.Fields.Append($tdf.CreateField("رقم_المرجع", 10, 50))
    $tdf.Fields.Append($tdf.CreateField("تاريخ_الحركة", 8))
    $tdf.Fields("تاريخ_الحركة").DefaultValue = "Now()"
    $tdf.Fields.Append($tdf.CreateField("الكمية", 7))
    $tdf.Fields.Append($tdf.CreateField("التكلفة_الوحدة", 6))
    $tdf.Fields("التكلفة_الوحدة").DefaultValue = "0"
    $tdf.Fields.Append($tdf.CreateField("إجمالي_التكلفة", 6))
    $tdf.Fields("إجمالي_التكلفة").DefaultValue = "0"
    $tdf.Fields.Append($tdf.CreateField("رقم_الدفعة", 10, 50))
    $tdf.Fields.Append($tdf.CreateField("تاريخ_انتهاء_الصلاحية", 8))
    $tdf.Fields.Append($tdf.CreateField("المستخدم", 10, 50))
    $tdf.Fields.Append($tdf.CreateField("ملاحظات", 12))
    
    $idx = $tdf.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idx.Fields.Append($idx.CreateField("رقم_الحركة"))
    $tdf.Indexes.Append($idx)
    
    $db.TableDefs.Append($tdf)
    Write-Host "تم إنشاء جدول حركات المخزون" -ForegroundColor Green
    
    # 9. جدول الوصفات
    $tdf = $db.CreateTableDef("الوصفات")
    $tdf.Fields.Append($tdf.CreateField("رقم_الوصفة", 4))
    $tdf.Fields("رقم_الوصفة").Attributes = 17
    $tdf.Fields.Append($tdf.CreateField("رقم_المنتج", 4))
    $tdf.Fields.Append($tdf.CreateField("اسم_الوصفة", 10, 200))
    $tdf.Fields.Append($tdf.CreateField("كمية_الإنتاج", 7))
    $tdf.Fields.Append($tdf.CreateField("وحدة_الإنتاج", 4))
    $tdf.Fields.Append($tdf.CreateField("تاريخ_الإنشاء", 8))
    $tdf.Fields("تاريخ_الإنشاء").DefaultValue = "Now()"
    $tdf.Fields.Append($tdf.CreateField("تاريخ_آخر_تعديل", 8))
    $tdf.Fields("تاريخ_آخر_تعديل").DefaultValue = "Now()"
    $tdf.Fields.Append($tdf.CreateField("نشط", 1))
    $tdf.Fields("نشط").DefaultValue = "True"
    $tdf.Fields.Append($tdf.CreateField("ملاحظات", 12))

    $idx = $tdf.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idx.Fields.Append($idx.CreateField("رقم_الوصفة"))
    $tdf.Indexes.Append($idx)

    $db.TableDefs.Append($tdf)
    Write-Host "تم إنشاء جدول الوصفات" -ForegroundColor Green

    # 10. جدول مكونات الوصفة
    $tdf = $db.CreateTableDef("مكونات_الوصفة")
    $tdf.Fields.Append($tdf.CreateField("رقم_المكون", 4))
    $tdf.Fields("رقم_المكون").Attributes = 17
    $tdf.Fields.Append($tdf.CreateField("رقم_الوصفة", 4))
    $tdf.Fields.Append($tdf.CreateField("رقم_المادة", 4))
    $tdf.Fields.Append($tdf.CreateField("الكمية_المطلوبة", 7))
    $tdf.Fields.Append($tdf.CreateField("وحدة_القياس", 4))
    $tdf.Fields.Append($tdf.CreateField("التكلفة_المعيارية", 6))
    $tdf.Fields("التكلفة_المعيارية").DefaultValue = "0"
    $tdf.Fields.Append($tdf.CreateField("نسبة_الفاقد", 7))
    $tdf.Fields("نسبة_الفاقد").DefaultValue = "0"
    $tdf.Fields.Append($tdf.CreateField("ملاحظات", 12))

    $idx = $tdf.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idx.Fields.Append($idx.CreateField("رقم_المكون"))
    $tdf.Indexes.Append($idx)

    $db.TableDefs.Append($tdf)
    Write-Host "تم إنشاء جدول مكونات الوصفة" -ForegroundColor Green

    # 11. جدول أوامر الإنتاج
    $tdf = $db.CreateTableDef("أوامر_الإنتاج")
    $tdf.Fields.Append($tdf.CreateField("رقم_أمر_الإنتاج", 4))
    $tdf.Fields("رقم_أمر_الإنتاج").Attributes = 17
    $tdf.Fields.Append($tdf.CreateField("رقم_الوصفة", 4))
    $tdf.Fields.Append($tdf.CreateField("كمية_الإنتاج_المطلوبة", 7))
    $tdf.Fields.Append($tdf.CreateField("تاريخ_الأمر", 8))
    $tdf.Fields("تاريخ_الأمر").DefaultValue = "Now()"
    $tdf.Fields.Append($tdf.CreateField("تاريخ_البدء_المخطط", 8))
    $tdf.Fields.Append($tdf.CreateField("تاريخ_الانتهاء_المخطط", 8))
    $tdf.Fields.Append($tdf.CreateField("تاريخ_البدء_الفعلي", 8))
    $tdf.Fields.Append($tdf.CreateField("تاريخ_الانتهاء_الفعلي", 8))
    $tdf.Fields.Append($tdf.CreateField("حالة_الأمر", 10, 20))
    $tdf.Fields("حالة_الأمر").DefaultValue = "'مخطط'"
    $tdf.Fields.Append($tdf.CreateField("رقم_المستودع", 4))
    $tdf.Fields.Append($tdf.CreateField("التكلفة_المخططة", 6))
    $tdf.Fields("التكلفة_المخططة").DefaultValue = "0"
    $tdf.Fields.Append($tdf.CreateField("التكلفة_الفعلية", 6))
    $tdf.Fields("التكلفة_الفعلية").DefaultValue = "0"
    $tdf.Fields.Append($tdf.CreateField("المسؤول", 10, 100))
    $tdf.Fields.Append($tdf.CreateField("ملاحظات", 12))

    $idx = $tdf.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idx.Fields.Append($idx.CreateField("رقم_أمر_الإنتاج"))
    $tdf.Indexes.Append($idx)

    $db.TableDefs.Append($tdf)
    Write-Host "تم إنشاء جدول أوامر الإنتاج" -ForegroundColor Green

    # إدراج البيانات الأساسية
    Write-Host "إدراج البيانات الأساسية..." -ForegroundColor Cyan

    # وحدات القياس
    $rs = $db.OpenRecordset("وحدات_القياس")
    $rs.AddNew()
    $rs.Fields("اسم_الوحدة").Value = "كيلوجرام"
    $rs.Fields("الرمز").Value = "كجم"
    $rs.Fields("نوع_الوحدة").Value = "وزن"
    $rs.Fields("معامل_التحويل").Value = 1
    $rs.Update()

    $rs.AddNew()
    $rs.Fields("اسم_الوحدة").Value = "جرام"
    $rs.Fields("الرمز").Value = "جم"
    $rs.Fields("نوع_الوحدة").Value = "وزن"
    $rs.Fields("معامل_التحويل").Value = 0.001
    $rs.Update()

    $rs.AddNew()
    $rs.Fields("اسم_الوحدة").Value = "قطعة"
    $rs.Fields("الرمز").Value = "قطعة"
    $rs.Fields("نوع_الوحدة").Value = "عدد"
    $rs.Fields("معامل_التحويل").Value = 1
    $rs.Update()

    $rs.AddNew()
    $rs.Fields("اسم_الوحدة").Value = "علبة"
    $rs.Fields("الرمز").Value = "علبة"
    $rs.Fields("نوع_الوحدة").Value = "تعبئة"
    $rs.Fields("معامل_التحويل").Value = 1
    $rs.Update()

    $rs.AddNew()
    $rs.Fields("اسم_الوحدة").Value = "لتر"
    $rs.Fields("الرمز").Value = "لتر"
    $rs.Fields("نوع_الوحدة").Value = "حجم"
    $rs.Fields("معامل_التحويل").Value = 1
    $rs.Update()

    $rs.Close()
    Write-Host "تم إدراج وحدات القياس" -ForegroundColor Green

    Write-Host "تم إنشاء جميع الجداول الأساسية بنجاح!" -ForegroundColor Green

} catch {
    Write-Host "خطأ في إنشاء النظام: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # تنظيف الكائنات
    if ($rs) { $rs.Close() }
    if ($db) { $db.Close() }
    if ($dao) { [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dao) | Out-Null }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}

Write-Host "انتهى إنشاء النظام الأساسي. الخطوة التالية: إضافة البيانات والعلاقات..." -ForegroundColor Yellow
