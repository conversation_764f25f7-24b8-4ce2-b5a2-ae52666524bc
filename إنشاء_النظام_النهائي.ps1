# إنشاء نظام إدارة تصنيع المعمول النهائي
# يتضمن إنشاء قاعدة البيانات كاملة مع جميع الجداول والبيانات

Write-Host "=== نظام إدارة تصنيع المعمول - الإصدار النهائي ===" -ForegroundColor Cyan
Write-Host "نظام شامل لإدارة تصنيع معمول الجوز والعسل" -ForegroundColor White
Write-Host ""

try {
    # حذف قاعدة البيانات القديمة إن وجدت
    $dbPath = "نظام_إدارة_تصنيع_المعمول.accdb"
    if (Test-Path $dbPath) {
        Remove-Item $dbPath -Force
        Write-Host "تم حذف قاعدة البيانات القديمة" -ForegroundColor Yellow
    }
    
    Write-Host "=== المرحلة 1: إنشاء قاعدة البيانات والجداول الأساسية ===" -ForegroundColor Yellow
    & ".\إنشاء_قاعدة_البيانات_محسن.ps1"
    
    Write-Host "=== المرحلة 2: إكمال الجداول ===" -ForegroundColor Yellow
    & ".\إكمال_الجداول.ps1"
    
    Write-Host "=== المرحلة 3: إنشاء جداول التصنيع ===" -ForegroundColor Yellow
    & ".\إنشاء_جداول_التصنيع.ps1"
    
    Write-Host "=== المرحلة 4: إدراج البيانات الأساسية ===" -ForegroundColor Yellow
    & ".\إدراج_البيانات_الأساسية.ps1"
    
    Write-Host "=== المرحلة 5: إنشاء الوصفة والمثال ===" -ForegroundColor Yellow
    & ".\إنشاء_الوصفة_والمثال.ps1"
    
    Write-Host ""
    Write-Host "=== تم إنشاء النظام بنجاح! ===" -ForegroundColor Green
    Write-Host ""
    
    # عرض ملخص النظام
    Write-Host "=== ملخص نظام إدارة تصنيع المعمول ===" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "📊 مكونات النظام:" -ForegroundColor White
    Write-Host "  ✅ قاعدة بيانات Microsoft Access" -ForegroundColor Green
    Write-Host "  ✅ 15+ جدول لإدارة العمليات" -ForegroundColor Green
    Write-Host "  ✅ نظام محاسبة التكاليف" -ForegroundColor Green
    Write-Host "  ✅ إدارة المخزون متعدد المستودعات" -ForegroundColor Green
    Write-Host "  ✅ تتبع الدفعات وتواريخ الانتهاء" -ForegroundColor Green
    Write-Host "  ✅ إدارة الوصفات والمكونات" -ForegroundColor Green
    Write-Host "  ✅ تقارير شاملة" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "🏭 المثال التطبيقي:" -ForegroundColor White
    Write-Host "  📦 المنتج: معمول الجوز والعسل 600 جرام" -ForegroundColor Cyan
    Write-Host "  🔢 الكمية: 100 قطعة" -ForegroundColor Cyan
    Write-Host "  💰 التكلفة: 24.65 جنيه للقطعة" -ForegroundColor Cyan
    Write-Host "  💵 سعر البيع: 85.00 جنيه للقطعة" -ForegroundColor Cyan
    Write-Host "  📈 هامش الربح: 244.88%" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "📋 المواد الخام المطلوبة:" -ForegroundColor White
    Write-Host "  • دقيق قمح: 30 كجم (255 جنيه)" -ForegroundColor Cyan
    Write-Host "  • جوز مقشر: 8 كجم (360 جنيه)" -ForegroundColor Cyan
    Write-Host "  • عسل نحل: 5 كجم (400 جنيه)" -ForegroundColor Cyan
    Write-Host "  • زبدة طبيعية: 6 كجم (150 جنيه)" -ForegroundColor Cyan
    Write-Host "  • سكر ناعم: 4 كجم (48 جنيه)" -ForegroundColor Cyan
    Write-Host "  • بيكنج باودر: 200 جرام (3 جنيه)" -ForegroundColor Cyan
    Write-Host "  • ملح طعام: 50 جرام (0.15 جنيه)" -ForegroundColor Cyan
    Write-Host "  • علب كرتونية: 100 قطعة (250 جنيه)" -ForegroundColor Cyan
    Write-Host "  📊 إجمالي تكلفة المواد: 1,466.15 جنيه" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "💼 التكاليف الإضافية:" -ForegroundColor White
    Write-Host "  • تكلفة العمالة: 800 جنيه" -ForegroundColor Cyan
    Write-Host "  • التكاليف الإضافية: 200 جنيه" -ForegroundColor Cyan
    Write-Host "  📊 إجمالي التكلفة: 2,466.15 جنيه" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "📈 تحليل الربحية:" -ForegroundColor White
    Write-Host "  💰 إجمالي الإيرادات (100 قطعة): 8,500 جنيه" -ForegroundColor Green
    Write-Host "  💸 إجمالي التكلفة: 2,466.15 جنيه" -ForegroundColor Red
    Write-Host "  💵 صافي الربح: 6,033.85 جنيه" -ForegroundColor Green
    Write-Host "  📊 هامش الربح: 244.88%" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "🗂️ الجداول المنشأة:" -ForegroundColor White
    Write-Host "  1. وحدات_القياس - إدارة وحدات القياس" -ForegroundColor Cyan
    Write-Host "  2. المستودعات - إدارة المستودعات" -ForegroundColor Cyan
    Write-Host "  3. فئات_المواد - تصنيف المواد" -ForegroundColor Cyan
    Write-Host "  4. المواد_والمنتجات - قائمة المواد والمنتجات" -ForegroundColor Cyan
    Write-Host "  5. الموردين - بيانات الموردين" -ForegroundColor Cyan
    Write-Host "  6. العملاء - بيانات العملاء" -ForegroundColor Cyan
    Write-Host "  7. أرصدة_المخزون - أرصدة المواد في المستودعات" -ForegroundColor Cyan
    Write-Host "  8. حركات_المخزون - تتبع جميع حركات المخزون" -ForegroundColor Cyan
    Write-Host "  9. الوصفات - وصفات الإنتاج" -ForegroundColor Cyan
    Write-Host "  10. مكونات_الوصفة - مكونات كل وصفة" -ForegroundColor Cyan
    Write-Host "  11. مراكز_التكلفة - مراكز التكلفة للإنتاج" -ForegroundColor Cyan
    Write-Host "  12. أوامر_الإنتاج - أوامر الإنتاج" -ForegroundColor Cyan
    Write-Host "  13. تفاصيل_أوامر_الإنتاج - تفاصيل أوامر الإنتاج" -ForegroundColor Cyan
    Write-Host "  14. أوامر_الشراء - أوامر الشراء" -ForegroundColor Cyan
    Write-Host "  15. تفاصيل_أوامر_الشراء - تفاصيل أوامر الشراء" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "🔧 الميزات المتقدمة:" -ForegroundColor White
    Write-Host "  ✅ نظام محاسبة التكاليف بالمتوسط المرجح" -ForegroundColor Green
    Write-Host "  ✅ تتبع الدفعات ورقم اللوت" -ForegroundColor Green
    Write-Host "  ✅ إدارة تواريخ الانتهاء" -ForegroundColor Green
    Write-Host "  ✅ تحديد الحد الأدنى للمخزون" -ForegroundColor Green
    Write-Host "  ✅ دعم وحدات قياس متعددة" -ForegroundColor Green
    Write-Host "  ✅ تقارير المخزون والحركات" -ForegroundColor Green
    Write-Host "  ✅ تحليل التكاليف والربحية" -ForegroundColor Green
    Write-Host "  ✅ واجهة عربية كاملة" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "📁 ملفات النظام:" -ForegroundColor White
    Write-Host "  📄 نظام_إدارة_تصنيع_المعمول.accdb - قاعدة البيانات الرئيسية" -ForegroundColor Cyan
    Write-Host "  📄 نظام_إدارة_تصنيع_المعمول.sql - سكريبت SQL للنظام" -ForegroundColor Cyan
    Write-Host "  📄 مجموعة سكريبتات PowerShell للإنشاء والإدارة" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "🚀 كيفية الاستخدام:" -ForegroundColor White
    Write-Host "  1. افتح ملف: نظام_إدارة_تصنيع_المعمول.accdb" -ForegroundColor Yellow
    Write-Host "  2. استعرض الجداول والبيانات" -ForegroundColor Yellow
    Write-Host "  3. استخدم النماذج لإدخال البيانات" -ForegroundColor Yellow
    Write-Host "  4. اطلع على التقارير للتحليل" -ForegroundColor Yellow
    Write-Host "  5. قم بتخصيص النظام حسب احتياجاتك" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "⚠️ ملاحظات مهمة:" -ForegroundColor White
    Write-Host "  • النظام مصمم لبيئة Windows مع Microsoft Access" -ForegroundColor Yellow
    Write-Host "  • يدعم اللغة العربية بالكامل" -ForegroundColor Yellow
    Write-Host "  • قابل للتوسع والتخصيص" -ForegroundColor Yellow
    Write-Host "  • يتضمن نظام نسخ احتياطي" -ForegroundColor Yellow
    Write-Host "  • مناسب للشركات الصغيرة والمتوسطة" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "=== تم إنشاء نظام إدارة تصنيع المعمول بنجاح! ===" -ForegroundColor Green
    Write-Host "النظام جاهز للاستخدام الفوري" -ForegroundColor Cyan
    Write-Host ""
    
    # عرض معلومات الملف
    if (Test-Path $dbPath) {
        $fileInfo = Get-Item $dbPath
        Write-Host "📊 معلومات الملف:" -ForegroundColor White
        Write-Host "  📁 المسار: $($fileInfo.FullName)" -ForegroundColor Cyan
        Write-Host "  📏 الحجم: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor Cyan
        Write-Host "  📅 تاريخ الإنشاء: $($fileInfo.CreationTime)" -ForegroundColor Cyan
        Write-Host ""
    }
    
    Write-Host "🎉 شكراً لاستخدام نظام إدارة تصنيع المعمول!" -ForegroundColor Green
    Write-Host "للدعم الفني أو التطوير، يرجى التواصل مع فريق التطوير" -ForegroundColor Cyan
    
} catch {
    Write-Host "حدث خطأ أثناء إنشاء النظام: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "يرجى التحقق من وجود Microsoft Access والصلاحيات المطلوبة" -ForegroundColor Yellow
    exit 1
}
