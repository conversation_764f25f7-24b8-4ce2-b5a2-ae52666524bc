# السكريبت الرئيسي لإنشاء نظام إدارة تصنيع المعمول الكامل والصحيح
# Main Script for Creating Complete and Correct Ma'amoul Manufacturing Management System

Write-Host "===============================================" -ForegroundColor Magenta
Write-Host "    نظام إدارة تصنيع المعمول الكامل والصحيح    " -ForegroundColor Magenta
Write-Host "   Complete Ma'amoul Manufacturing System    " -ForegroundColor Magenta
Write-Host "===============================================" -ForegroundColor Magenta
Write-Host ""

Write-Host "بدء إنشاء النظام الكامل مع جميع المكونات..." -ForegroundColor Green
Write-Host "- الجداول مع العلاقات الصحيحة" -ForegroundColor Cyan
Write-Host "- الاستعلامات الوظيفية" -ForegroundColor Cyan
Write-Host "- النماذج التفاعلية" -ForegroundColor Cyan
Write-Host "- التقارير الشاملة" -ForegroundColor Cyan
Write-Host "- البيانات التجريبية الكاملة" -ForegroundColor Cyan
Write-Host ""

$startTime = Get-Date

try {
    # الخطوة 1: إنشاء قاعدة البيانات الأساسية مع العلاقات
    Write-Host "الخطوة 1: إنشاء قاعدة البيانات الأساسية مع العلاقات..." -ForegroundColor Yellow
    if (Test-Path "إنشاء_النظام_الكامل_مع_العلاقات.ps1") {
        & ".\إنشاء_النظام_الكامل_مع_العلاقات.ps1"
        if ($LASTEXITCODE -eq 0 -or $LASTEXITCODE -eq $null) {
            Write-Host "✓ تم إنشاء قاعدة البيانات والعلاقات بنجاح" -ForegroundColor Green
        } else {
            throw "فشل في إنشاء قاعدة البيانات"
        }
    } else {
        throw "ملف إنشاء قاعدة البيانات غير موجود"
    }
    
    Start-Sleep -Seconds 2
    
    # الخطوة 2: إنشاء الاستعلامات
    Write-Host "الخطوة 2: إنشاء الاستعلامات الوظيفية..." -ForegroundColor Yellow
    if (Test-Path "إنشاء_الاستعلامات_والنماذج_والتقارير.ps1") {
        & ".\إنشاء_الاستعلامات_والنماذج_والتقارير.ps1"
        if ($LASTEXITCODE -eq 0 -or $LASTEXITCODE -eq $null) {
            Write-Host "✓ تم إنشاء الاستعلامات بنجاح" -ForegroundColor Green
        } else {
            throw "فشل في إنشاء الاستعلامات"
        }
    } else {
        throw "ملف إنشاء الاستعلامات غير موجود"
    }
    
    Start-Sleep -Seconds 2
    
    # الخطوة 3: إنشاء النماذج
    Write-Host "الخطوة 3: إنشاء النماذج التفاعلية..." -ForegroundColor Yellow
    if (Test-Path "إنشاء_النماذج.ps1") {
        & ".\إنشاء_النماذج.ps1"
        if ($LASTEXITCODE -eq 0 -or $LASTEXITCODE -eq $null) {
            Write-Host "✓ تم إنشاء النماذج بنجاح" -ForegroundColor Green
        } else {
            throw "فشل في إنشاء النماذج"
        }
    } else {
        throw "ملف إنشاء النماذج غير موجود"
    }
    
    Start-Sleep -Seconds 2
    
    # الخطوة 4: إنشاء التقارير
    Write-Host "الخطوة 4: إنشاء التقارير الشاملة..." -ForegroundColor Yellow
    if (Test-Path "إنشاء_التقارير.ps1") {
        & ".\إنشاء_التقارير.ps1"
        if ($LASTEXITCODE -eq 0 -or $LASTEXITCODE -eq $null) {
            Write-Host "✓ تم إنشاء التقارير بنجاح" -ForegroundColor Green
        } else {
            throw "فشل في إنشاء التقارير"
        }
    } else {
        throw "ملف إنشاء التقارير غير موجود"
    }
    
    Start-Sleep -Seconds 2
    
    # الخطوة 5: إدراج البيانات التجريبية
    Write-Host "الخطوة 5: إدراج البيانات التجريبية الكاملة..." -ForegroundColor Yellow
    if (Test-Path "إدراج_البيانات_التجريبية_الكاملة.ps1") {
        & ".\إدراج_البيانات_التجريبية_الكاملة.ps1"
        if ($LASTEXITCODE -eq 0 -or $LASTEXITCODE -eq $null) {
            Write-Host "✓ تم إدراج البيانات التجريبية بنجاح" -ForegroundColor Green
        } else {
            throw "فشل في إدراج البيانات التجريبية"
        }
    } else {
        throw "ملف إدراج البيانات غير موجود"
    }
    
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    Write-Host ""
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host "          تم إنجاز النظام بنجاح كامل!          " -ForegroundColor Green
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "📊 ملخص النظام المكتمل:" -ForegroundColor Cyan
    Write-Host "✓ قاعدة البيانات مع العلاقات الصحيحة" -ForegroundColor Green
    Write-Host "✓ 7+ استعلامات وظيفية للتقارير والتحليل" -ForegroundColor Green
    Write-Host "✓ 12+ نموذج تفاعلي لإدارة البيانات" -ForegroundColor Green
    Write-Host "✓ 10+ تقرير شامل للمتابعة والتحليل" -ForegroundColor Green
    Write-Host "✓ بيانات تجريبية كاملة لمعمول الجوز والعسل" -ForegroundColor Green
    Write-Host "✓ واجهة عربية كاملة مع ترميز صحيح" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "🎯 المكونات الرئيسية:" -ForegroundColor Cyan
    Write-Host "• إدارة المخزون متعددة المستودعات" -ForegroundColor White
    Write-Host "• نظام محاسبة التكاليف بالمتوسط المرجح" -ForegroundColor White
    Write-Host "• إدارة الوصفات ومكونات الإنتاج" -ForegroundColor White
    Write-Host "• تتبع الدفعات وتواريخ انتهاء الصلاحية" -ForegroundColor White
    Write-Host "• أوامر الإنتاج والتخطيط" -ForegroundColor White
    Write-Host "• تقارير الأداء والربحية" -ForegroundColor White
    Write-Host "• تنبيهات المخزون والصلاحية" -ForegroundColor White
    Write-Host ""
    
    Write-Host "📁 ملف قاعدة البيانات: نظام_إدارة_تصنيع_المعمول.accdb" -ForegroundColor Yellow
    Write-Host "⏱️  وقت الإنشاء: $($duration.TotalMinutes.ToString('F1')) دقيقة" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "🚀 للبدء في الاستخدام:" -ForegroundColor Cyan
    Write-Host "1. افتح ملف: نظام_إدارة_تصنيع_المعمول.accdb" -ForegroundColor White
    Write-Host "2. استعرض الجداول والعلاقات" -ForegroundColor White
    Write-Host "3. جرب الاستعلامات والتقارير" -ForegroundColor White
    Write-Host "4. استخدم النماذج لإدخال البيانات" -ForegroundColor White
    Write-Host "5. راجع البيانات التجريبية المدرجة" -ForegroundColor White
    Write-Host ""
    
    # فتح قاعدة البيانات تلقائياً
    Write-Host "فتح قاعدة البيانات..." -ForegroundColor Green
    if (Test-Path "نظام_إدارة_تصنيع_المعمول.accdb") {
        Start-Process "نظام_إدارة_تصنيع_المعمول.accdb"
        Write-Host "✓ تم فتح قاعدة البيانات في Microsoft Access" -ForegroundColor Green
    }
    
} catch {
    Write-Host ""
    Write-Host "❌ خطأ في إنشاء النظام: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 للمساعدة في حل المشكلة:" -ForegroundColor Yellow
    Write-Host "1. تأكد من وجود Microsoft Access على النظام" -ForegroundColor White
    Write-Host "2. تأكد من صلاحيات الكتابة في المجلد الحالي" -ForegroundColor White
    Write-Host "3. أغلق أي ملفات Access مفتوحة" -ForegroundColor White
    Write-Host "4. شغل PowerShell كمدير (Run as Administrator)" -ForegroundColor White
    Write-Host ""
    exit 1
}

Write-Host "===============================================" -ForegroundColor Magenta
Write-Host "       النظام جاهز للاستخدام الفوري!         " -ForegroundColor Magenta
Write-Host "===============================================" -ForegroundColor Magenta
