# سكريبت إنشاء النماذج لنظام إدارة تصنيع المعمول
# Creating Forms for Ma'amoul Manufacturing Management System

Write-Host "بدء إنشاء النماذج..." -ForegroundColor Green

$dbPath = "نظام_إدارة_تصنيع_المعمول.accdb"

if (-not (Test-Path $dbPath)) {
    Write-Host "خطأ: قاعدة البيانات غير موجودة. يرجى تشغيل سكريبت إنشاء النظام أولاً." -ForegroundColor Red
    exit
}

try {
    # فتح Access
    $access = New-Object -ComObject Access.Application
    $access.OpenCurrentDatabase($dbPath)
    $access.Visible = $false
    
    Write-Host "إنشاء النماذج الأساسية..." -ForegroundColor Cyan
    
    # 1. نموذج إدارة المواد والمنتجات
    $access.DoCmd.OpenForm("المواد_والمنتجات", 0, "", "", 1, 1)
    $frm = $access.Forms("المواد_والمنتجات")
    
    # تخصيص النموذج
    $frm.Caption = "إدارة المواد والمنتجات"
    $frm.RecordSource = "المواد_والمنتجات"
    $frm.DefaultView = 0  # Single Form
    $frm.AllowAdditions = $true
    $frm.AllowDeletions = $true
    $frm.AllowEdits = $true
    $frm.NavigationButtons = $true
    $frm.RecordSelectors = $true
    $frm.DividingLines = $true
    
    $access.DoCmd.Close(2, "المواد_والمنتجات", 1)  # Save and close
    Write-Host "تم إنشاء نموذج إدارة المواد والمنتجات" -ForegroundColor Green
    
    # 2. نموذج إدارة المستودعات
    $access.DoCmd.OpenForm("المستودعات", 0, "", "", 1, 1)
    $frm = $access.Forms("المستودعات")
    
    $frm.Caption = "إدارة المستودعات"
    $frm.RecordSource = "المستودعات"
    $frm.DefaultView = 0
    $frm.AllowAdditions = $true
    $frm.AllowDeletions = $true
    $frm.AllowEdits = $true
    
    $access.DoCmd.Close(2, "المستودعات", 1)
    Write-Host "تم إنشاء نموذج إدارة المستودعات" -ForegroundColor Green
    
    # 3. نموذج إدارة الموردين
    $access.DoCmd.OpenForm("الموردين", 0, "", "", 1, 1)
    $frm = $access.Forms("الموردين")
    
    $frm.Caption = "إدارة الموردين"
    $frm.RecordSource = "الموردين"
    $frm.DefaultView = 0
    $frm.AllowAdditions = $true
    $frm.AllowDeletions = $true
    $frm.AllowEdits = $true
    
    $access.DoCmd.Close(2, "الموردين", 1)
    Write-Host "تم إنشاء نموذج إدارة الموردين" -ForegroundColor Green
    
    # 4. نموذج إدارة العملاء
    $access.DoCmd.OpenForm("العملاء", 0, "", "", 1, 1)
    $frm = $access.Forms("العملاء")
    
    $frm.Caption = "إدارة العملاء"
    $frm.RecordSource = "العملاء"
    $frm.DefaultView = 0
    $frm.AllowAdditions = $true
    $frm.AllowDeletions = $true
    $frm.AllowEdits = $true
    
    $access.DoCmd.Close(2, "العملاء", 1)
    Write-Host "تم إنشاء نموذج إدارة العملاء" -ForegroundColor Green
    
    # 5. نموذج حركات المخزون
    $access.DoCmd.OpenForm("حركات_المخزون", 0, "", "", 1, 1)
    $frm = $access.Forms("حركات_المخزون")
    
    $frm.Caption = "حركات المخزون"
    $frm.RecordSource = "استعلام_حركات_المخزون"
    $frm.DefaultView = 2  # Datasheet View
    $frm.AllowAdditions = $true
    $frm.AllowDeletions = $false
    $frm.AllowEdits = $false
    
    $access.DoCmd.Close(2, "حركات_المخزون", 1)
    Write-Host "تم إنشاء نموذج حركات المخزون" -ForegroundColor Green
    
    # 6. نموذج أرصدة المخزون
    $access.DoCmd.OpenForm("أرصدة_المخزون", 0, "", "", 1, 1)
    $frm = $access.Forms("أرصدة_المخزون")
    
    $frm.Caption = "أرصدة المخزون"
    $frm.RecordSource = "استعلام_أرصدة_المخزون"
    $frm.DefaultView = 2  # Datasheet View
    $frm.AllowAdditions = $false
    $frm.AllowDeletions = $false
    $frm.AllowEdits = $false
    
    $access.DoCmd.Close(2, "أرصدة_المخزون", 1)
    Write-Host "تم إنشاء نموذج أرصدة المخزون" -ForegroundColor Green
    
    # 7. نموذج إدارة الوصفات
    $access.DoCmd.OpenForm("الوصفات", 0, "", "", 1, 1)
    $frm = $access.Forms("الوصفات")
    
    $frm.Caption = "إدارة الوصفات"
    $frm.RecordSource = "الوصفات"
    $frm.DefaultView = 0
    $frm.AllowAdditions = $true
    $frm.AllowDeletions = $true
    $frm.AllowEdits = $true
    
    $access.DoCmd.Close(2, "الوصفات", 1)
    Write-Host "تم إنشاء نموذج إدارة الوصفات" -ForegroundColor Green
    
    # 8. نموذج مكونات الوصفة
    $access.DoCmd.OpenForm("مكونات_الوصفة", 0, "", "", 1, 1)
    $frm = $access.Forms("مكونات_الوصفة")
    
    $frm.Caption = "مكونات الوصفة"
    $frm.RecordSource = "استعلام_تفاصيل_الوصفة"
    $frm.DefaultView = 2  # Datasheet View
    $frm.AllowAdditions = $true
    $frm.AllowDeletions = $true
    $frm.AllowEdits = $true
    
    $access.DoCmd.Close(2, "مكونات_الوصفة", 1)
    Write-Host "تم إنشاء نموذج مكونات الوصفة" -ForegroundColor Green
    
    # 9. نموذج أوامر الإنتاج
    $access.DoCmd.OpenForm("أوامر_الإنتاج", 0, "", "", 1, 1)
    $frm = $access.Forms("أوامر_الإنتاج")
    
    $frm.Caption = "أوامر الإنتاج"
    $frm.RecordSource = "استعلام_أوامر_الإنتاج"
    $frm.DefaultView = 0
    $frm.AllowAdditions = $true
    $frm.AllowDeletions = $true
    $frm.AllowEdits = $true
    
    $access.DoCmd.Close(2, "أوامر_الإنتاج", 1)
    Write-Host "تم إنشاء نموذج أوامر الإنتاج" -ForegroundColor Green
    
    # 10. نموذج المواد تحت الحد الأدنى
    $access.DoCmd.OpenForm("المواد_تحت_الحد_الأدنى", 0, "", "", 1, 1)
    $frm = $access.Forms("المواد_تحت_الحد_الأدنى")
    
    $frm.Caption = "المواد تحت الحد الأدنى - تنبيهات"
    $frm.RecordSource = "استعلام_المواد_تحت_الحد_الأدنى"
    $frm.DefaultView = 2  # Datasheet View
    $frm.AllowAdditions = $false
    $frm.AllowDeletions = $false
    $frm.AllowEdits = $false
    
    $access.DoCmd.Close(2, "المواد_تحت_الحد_الأدنى", 1)
    Write-Host "تم إنشاء نموذج المواد تحت الحد الأدنى" -ForegroundColor Green
    
    # 11. نموذج المواد منتهية الصلاحية
    $access.DoCmd.OpenForm("المواد_منتهية_الصلاحية", 0, "", "", 1, 1)
    $frm = $access.Forms("المواد_منتهية_الصلاحية")
    
    $frm.Caption = "المواد منتهية الصلاحية - تنبيهات"
    $frm.RecordSource = "استعلام_المواد_منتهية_الصلاحية"
    $frm.DefaultView = 2  # Datasheet View
    $frm.AllowAdditions = $false
    $frm.AllowDeletions = $false
    $frm.AllowEdits = $false
    
    $access.DoCmd.Close(2, "المواد_منتهية_الصلاحية", 1)
    Write-Host "تم إنشاء نموذج المواد منتهية الصلاحية" -ForegroundColor Green
    
    # 12. نموذج تكلفة الوصفات
    $access.DoCmd.OpenForm("تكلفة_الوصفات", 0, "", "", 1, 1)
    $frm = $access.Forms("تكلفة_الوصفات")
    
    $frm.Caption = "تحليل تكلفة الوصفات"
    $frm.RecordSource = "استعلام_تكلفة_الوصفات"
    $frm.DefaultView = 2  # Datasheet View
    $frm.AllowAdditions = $false
    $frm.AllowDeletions = $false
    $frm.AllowEdits = $false
    
    $access.DoCmd.Close(2, "تكلفة_الوصفات", 1)
    Write-Host "تم إنشاء نموذج تكلفة الوصفات" -ForegroundColor Green
    
    Write-Host "تم إنشاء جميع النماذج بنجاح!" -ForegroundColor Green
    
} catch {
    Write-Host "خطأ في إنشاء النماذج: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # تنظيف الكائنات
    if ($access) { 
        $access.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null 
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}

Write-Host "انتهى إنشاء النماذج. الخطوة التالية: إنشاء التقارير..." -ForegroundColor Yellow
