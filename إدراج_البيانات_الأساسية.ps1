# سكريپت إدراج البيانات الأساسية والمثال التطبيقي
# تاريخ الإنشاء: 2025-09-19

Write-Host "بدء إدراج البيانات الأساسية والمثال التطبيقي..." -ForegroundColor Green

try {
    $dbPath = Join-<PERSON> (Get-Location) "نظام_إدارة_تصنيع_المعمول.accdb"
    
    # فتح قاعدة البيانات
    $dao = New-Object -ComObject DAO.DBEngine.120
    $db = $dao.OpenDatabase($dbPath)
    
    # إدراج فئات المواد
    Write-Host "إدراج فئات المواد..." -ForegroundColor Cyan
    $rs = $db.OpenRecordset("فئات_المواد")
    
    $rs.AddNew()
    $rs.Fields("اسم_الفئة").Value = "مواد خام"
    $rs.Fields("وصف_الفئة").Value = "المواد الخام المستخدمة في الإنتاج"
    $rs.Fields("رمز_الفئة").Value = "RAW"
    $rs.Update()
    
    $rs.AddNew()
    $rs.Fields("اسم_الفئة").Value = "منتجات نهائية"
    $rs.Fields("وصف_الفئة").Value = "المنتجات الجاهزة للبيع"
    $rs.Fields("رمز_الفئة").Value = "FIN"
    $rs.Update()
    
    $rs.AddNew()
    $rs.Fields("اسم_الفئة").Value = "مواد التعبئة والتغليف"
    $rs.Fields("وصف_الفئة").Value = "مواد التعبئة والتغليف"
    $rs.Fields("رمز_الفئة").Value = "PKG"
    $rs.Update()
    
    $rs.Close()
    Write-Host "تم إدراج فئات المواد" -ForegroundColor Green
    
    # إدراج المواد الخام
    Write-Host "إدراج المواد الخام..." -ForegroundColor Cyan
    $rs = $db.OpenRecordset("المواد_والمنتجات")
    
    # دقيق القمح
    $rs.AddNew()
    $rs.Fields("كود_المادة").Value = "RAW001"
    $rs.Fields("اسم_المادة").Value = "دقيق القمح الفاخر"
    $rs.Fields("وصف_المادة").Value = "دقيق قمح عالي الجودة للمعجنات"
    $rs.Fields("رقم_الفئة").Value = 1
    $rs.Fields("نوع_المادة").Value = "مادة خام"
    $rs.Fields("وحدة_القياس_الأساسية").Value = 1 # كيلوجرام
    $rs.Fields("وحدة_الشراء").Value = 1
    $rs.Fields("وحدة_البيع").Value = 1
    $rs.Fields("الحد_الأدنى_للمخزون").Value = 100
    $rs.Fields("الحد_الأقصى_للمخزون").Value = 1000
    $rs.Fields("نقطة_إعادة_الطلب").Value = 200
    $rs.Fields("مدة_الصلاحية_بالأيام").Value = 180
    $rs.Fields("يتطلب_تتبع_الدفعات").Value = $true
    $rs.Fields("يتطلب_تتبع_الصلاحية").Value = $true
    $rs.Fields("التكلفة_المعيارية").Value = 8.50
    $rs.Update()
    
    # جوز مقشر
    $rs.AddNew()
    $rs.Fields("كود_المادة").Value = "RAW002"
    $rs.Fields("اسم_المادة").Value = "جوز مقشر ومفروم"
    $rs.Fields("وصف_المادة").Value = "جوز طبيعي مقشر ومفروم ناعم"
    $rs.Fields("رقم_الفئة").Value = 1
    $rs.Fields("نوع_المادة").Value = "مادة خام"
    $rs.Fields("وحدة_القياس_الأساسية").Value = 1 # كيلوجرام
    $rs.Fields("وحدة_الشراء").Value = 1
    $rs.Fields("وحدة_البيع").Value = 1
    $rs.Fields("الحد_الأدنى_للمخزون").Value = 50
    $rs.Fields("الحد_الأقصى_للمخزون").Value = 500
    $rs.Fields("نقطة_إعادة_الطلب").Value = 100
    $rs.Fields("مدة_الصلاحية_بالأيام").Value = 365
    $rs.Fields("يتطلب_تتبع_الدفعات").Value = $true
    $rs.Fields("يتطلب_تتبع_الصلاحية").Value = $true
    $rs.Fields("التكلفة_المعيارية").Value = 45.00
    $rs.Update()

    # عسل نحل طبيعي
    $rs.AddNew()
    $rs.Fields("كود_المادة").Value = "RAW003"
    $rs.Fields("اسم_المادة").Value = "عسل نحل طبيعي"
    $rs.Fields("وصف_المادة").Value = "عسل نحل طبيعي خالص"
    $rs.Fields("رقم_الفئة").Value = 1
    $rs.Fields("نوع_المادة").Value = "مادة خام"
    $rs.Fields("وحدة_القياس_الأساسية").Value = 1 # كيلوجرام
    $rs.Fields("وحدة_الشراء").Value = 1
    $rs.Fields("وحدة_البيع").Value = 1
    $rs.Fields("الحد_الأدنى_للمخزون").Value = 20
    $rs.Fields("الحد_الأقصى_للمخزون").Value = 200
    $rs.Fields("نقطة_إعادة_الطلب").Value = 50
    $rs.Fields("مدة_الصلاحية_بالأيام").Value = 730
    $rs.Fields("يتطلب_تتبع_الدفعات").Value = $true
    $rs.Fields("يتطلب_تتبع_الصلاحية").Value = $true
    $rs.Fields("التكلفة_المعيارية").Value = 80.00
    $rs.Update()

    # زبدة طبيعية
    $rs.AddNew()
    $rs.Fields("كود_المادة").Value = "RAW004"
    $rs.Fields("اسم_المادة").Value = "زبدة طبيعية"
    $rs.Fields("وصف_المادة").Value = "زبدة طبيعية عالية الجودة"
    $rs.Fields("رقم_الفئة").Value = 1
    $rs.Fields("نوع_المادة").Value = "مادة خام"
    $rs.Fields("وحدة_القياس_الأساسية").Value = 1 # كيلوجرام
    $rs.Fields("وحدة_الشراء").Value = 1
    $rs.Fields("وحدة_البيع").Value = 1
    $rs.Fields("الحد_الأدنى_للمخزون").Value = 30
    $rs.Fields("الحد_الأقصى_للمخزون").Value = 300
    $rs.Fields("نقطة_إعادة_الطلب").Value = 75
    $rs.Fields("مدة_الصلاحية_بالأيام").Value = 90
    $rs.Fields("يتطلب_تتبع_الدفعات").Value = $true
    $rs.Fields("يتطلب_تتبع_الصلاحية").Value = $true
    $rs.Fields("التكلفة_المعيارية").Value = 25.00
    $rs.Update()

    # سكر ناعم
    $rs.AddNew()
    $rs.Fields("كود_المادة").Value = "RAW005"
    $rs.Fields("اسم_المادة").Value = "سكر ناعم"
    $rs.Fields("وصف_المادة").Value = "سكر أبيض ناعم للحلويات"
    $rs.Fields("رقم_الفئة").Value = 1
    $rs.Fields("نوع_المادة").Value = "مادة خام"
    $rs.Fields("وحدة_القياس_الأساسية").Value = 1 # كيلوجرام
    $rs.Fields("وحدة_الشراء").Value = 1
    $rs.Fields("وحدة_البيع").Value = 1
    $rs.Fields("الحد_الأدنى_للمخزون").Value = 50
    $rs.Fields("الحد_الأقصى_للمخزون").Value = 500
    $rs.Fields("نقطة_إعادة_الطلب").Value = 100
    $rs.Fields("مدة_الصلاحية_بالأيام").Value = 365
    $rs.Fields("يتطلب_تتبع_الدفعات").Value = $false
    $rs.Fields("يتطلب_تتبع_الصلاحية").Value = $false
    $rs.Fields("التكلفة_المعيارية").Value = 12.00
    $rs.Update()

    # بيكنج باودر
    $rs.AddNew()
    $rs.Fields("كود_المادة").Value = "RAW006"
    $rs.Fields("اسم_المادة").Value = "بيكنج باودر"
    $rs.Fields("وصف_المادة").Value = "مسحوق الخبز للمعجنات"
    $rs.Fields("رقم_الفئة").Value = 1
    $rs.Fields("نوع_المادة").Value = "مادة خام"
    $rs.Fields("وحدة_القياس_الأساسية").Value = 2 # جرام
    $rs.Fields("وحدة_الشراء").Value = 1 # كيلوجرام
    $rs.Fields("وحدة_البيع").Value = 2 # جرام
    $rs.Fields("الحد_الأدنى_للمخزون").Value = 5
    $rs.Fields("الحد_الأقصى_للمخزون").Value = 50
    $rs.Fields("نقطة_إعادة_الطلب").Value = 10
    $rs.Fields("مدة_الصلاحية_بالأيام").Value = 365
    $rs.Fields("يتطلب_تتبع_الدفعات").Value = $true
    $rs.Fields("يتطلب_تتبع_الصلاحية").Value = $true
    $rs.Fields("التكلفة_المعيارية").Value = 0.015
    $rs.Update()

    # ملح طعام
    $rs.AddNew()
    $rs.Fields("كود_المادة").Value = "RAW007"
    $rs.Fields("اسم_المادة").Value = "ملح طعام ناعم"
    $rs.Fields("وصف_المادة").Value = "ملح طعام ناعم للمعجنات"
    $rs.Fields("رقم_الفئة").Value = 1
    $rs.Fields("نوع_المادة").Value = "مادة خام"
    $rs.Fields("وحدة_القياس_الأساسية").Value = 2 # جرام
    $rs.Fields("وحدة_الشراء").Value = 1 # كيلوجرام
    $rs.Fields("وحدة_البيع").Value = 2 # جرام
    $rs.Fields("الحد_الأدنى_للمخزون").Value = 10
    $rs.Fields("الحد_الأقصى_للمخزون").Value = 100
    $rs.Fields("نقطة_إعادة_الطلب").Value = 25
    $rs.Fields("مدة_الصلاحية_بالأيام").Value = 1095
    $rs.Fields("يتطلب_تتبع_الدفعات").Value = $false
    $rs.Fields("يتطلب_تتبع_الصلاحية").Value = $false
    $rs.Fields("التكلفة_المعيارية").Value = 0.003
    $rs.Update()

    # علب كرتونية 600 جرام
    $rs.AddNew()
    $rs.Fields("كود_المادة").Value = "PKG001"
    $rs.Fields("اسم_المادة").Value = "علبة كرتونية 600 جرام"
    $rs.Fields("وصف_المادة").Value = "علبة كرتونية مقواة لتعبئة المعمول 600 جرام"
    $rs.Fields("رقم_الفئة").Value = 3
    $rs.Fields("نوع_المادة").Value = "مادة تعبئة"
    $rs.Fields("وحدة_القياس_الأساسية").Value = 3 # قطعة
    $rs.Fields("وحدة_الشراء").Value = 5 # كرتونة
    $rs.Fields("وحدة_البيع").Value = 3 # قطعة
    $rs.Fields("الحد_الأدنى_للمخزون").Value = 500
    $rs.Fields("الحد_الأقصى_للمخزون").Value = 5000
    $rs.Fields("نقطة_إعادة_الطلب").Value = 1000
    $rs.Fields("مدة_الصلاحية_بالأيام").Value = 1095
    $rs.Fields("يتطلب_تتبع_الدفعات").Value = $false
    $rs.Fields("يتطلب_تتبع_الصلاحية").Value = $false
    $rs.Fields("التكلفة_المعيارية").Value = 2.50
    $rs.Update()

    # المنتج النهائي - معمول الجوز والعسل
    $rs.AddNew()
    $rs.Fields("كود_المادة").Value = "FIN001"
    $rs.Fields("اسم_المادة").Value = "معمول الجوز والعسل 600 جرام"
    $rs.Fields("وصف_المادة").Value = "معمول فاخر بالجوز والعسل الطبيعي - عبوة 600 جرام"
    $rs.Fields("رقم_الفئة").Value = 2
    $rs.Fields("نوع_المادة").Value = "منتج نهائي"
    $rs.Fields("وحدة_القياس_الأساسية").Value = 3 # قطعة
    $rs.Fields("وحدة_الشراء").Value = 3 # قطعة
    $rs.Fields("وحدة_البيع").Value = 3 # قطعة
    $rs.Fields("وحدة_الإنتاج").Value = 3 # قطعة
    $rs.Fields("الحد_الأدنى_للمخزون").Value = 50
    $rs.Fields("الحد_الأقصى_للمخزون").Value = 1000
    $rs.Fields("نقطة_إعادة_الطلب").Value = 100
    $rs.Fields("مدة_الصلاحية_بالأيام").Value = 30
    $rs.Fields("يتطلب_تتبع_الدفعات").Value = $true
    $rs.Fields("يتطلب_تتبع_الصلاحية").Value = $true
    $rs.Fields("التكلفة_المعيارية").Value = 0 # سيتم حسابها من الوصفة
    $rs.Fields("سعر_البيع_المقترح").Value = 85.00
    $rs.Fields("معدل_الضريبة").Value = 14
    $rs.Update()
    
    $rs.Close()
    Write-Host "تم إدراج المواد والمنتجات" -ForegroundColor Green
    
    # إدراج مراكز التكلفة
    Write-Host "إدراج مراكز التكلفة..." -ForegroundColor Cyan
    $rs = $db.OpenRecordset("مراكز_التكلفة")
    
    $rs.AddNew()
    $rs.Fields("كود_مركز_التكلفة").Value = "PROD001"
    $rs.Fields("اسم_مركز_التكلفة").Value = "خط إنتاج المعمول الرئيسي"
    $rs.Fields("نوع_مركز_التكلفة").Value = "إنتاج"
    $rs.Fields("وصف_المركز").Value = "خط الإنتاج الرئيسي لتصنيع المعمول"
    $rs.Fields("معدل_التحميل_للساعة").Value = 50.00
    $rs.Fields("التكاليف_الثابتة_الشهرية").Value = 15000.00
    $rs.Fields("السعة_الإنتاجية_الشهرية").Value = 10000
    $rs.Fields("وحدة_السعة").Value = "قطعة"
    $rs.Fields("المسؤول").Value = "أحمد محمد الإنتاج"
    $rs.Update()
    
    $rs.AddNew()
    $rs.Fields("كود_مركز_التكلفة").Value = "PACK001"
    $rs.Fields("اسم_مركز_التكلفة").Value = "قسم التعبئة والتغليف"
    $rs.Fields("نوع_مركز_التكلفة").Value = "إنتاج"
    $rs.Fields("وصف_المركز").Value = "قسم التعبئة والتغليف النهائي"
    $rs.Fields("معدل_التحميل_للساعة").Value = 30.00
    $rs.Fields("التكاليف_الثابتة_الشهرية").Value = 8000.00
    $rs.Fields("السعة_الإنتاجية_الشهرية").Value = 15000
    $rs.Fields("وحدة_السعة").Value = "قطعة"
    $rs.Fields("المسؤول").Value = "فاطمة علي التعبئة"
    $rs.Update()
    
    $rs.Close()
    Write-Host "تم إدراج مراكز التكلفة" -ForegroundColor Green
    
    Write-Host "تم إدراج جميع البيانات الأساسية بنجاح!" -ForegroundColor Green
    
    # إغلاق قاعدة البيانات
    $db.Close()
    
} catch {
    Write-Host "حدث خطأ: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($rs) { try { $rs.Close() } catch {} }
    if ($db) { try { $db.Close() } catch {} }
    if ($dao) {
        try {
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dao) | Out-Null
        } catch {}
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}
