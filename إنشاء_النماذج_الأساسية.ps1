# سكريپت إنشاء النماذج الأساسية
# تاريخ الإنشاء: 2025-09-19

Write-Host "بدء إنشاء النماذج الأساسية..." -ForegroundColor Green

try {
    $dbPath = Join-Path (Get-Location) "نظام_إدارة_تصنيع_المعمول.accdb"
    
    # فتح Access
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    $access.OpenCurrentDatabase($dbPath)
    
    Write-Host "تم فتح قاعدة البيانات في Access" -ForegroundColor Green
    
    # إنشاء النموذج الرئيسي للتنقل
    Write-Host "إنشاء النموذج الرئيسي..." -ForegroundColor Cyan
    
    # إنشاء نموذج فارغ
    $form = $access.CreateForm()
    $form.Caption = "نظام إدارة تصنيع معمول أبو فرح"
    $form.RecordSource = ""
    $form.DefaultView = 0 # Single Form
    $form.ScrollBars = 0 # None
    $form.RecordSelectors = $false
    $form.NavigationButtons = $false
    $form.DividingLines = $false
    $form.AutoCenter = $true
    $form.BorderStyle = 3 # Dialog
    $form.ControlBox = $true
    $form.MinMaxButtons = 0 # None
    $form.CloseButton = $true
    
    # تعيين الألوان والخط العربي
    $form.BackColor = 16777215 # أبيض
    $form.Section(0).BackColor = 12632256 # لون خلفية فاتح
    
    # حفظ النموذج
    $access.DoCmd.Save(2, "النموذج_الرئيسي")
    $access.DoCmd.Close(2, "النموذج_الرئيسي") # acForm
    
    Write-Host "تم إنشاء النموذج الرئيسي" -ForegroundColor Green
    
    # إنشاء نموذج إدارة المواد
    Write-Host "إنشاء نموذج إدارة المواد..." -ForegroundColor Cyan
    
    $form = $access.CreateForm("المواد_والمنتجات")
    $form.Caption = "إدارة المواد والمنتجات"
    $form.DefaultView = 0 # Single Form
    $form.AllowAdditions = $true
    $form.AllowDeletions = $true
    $form.AllowEdits = $true
    $form.DataEntry = $false
    $form.RecordSelectors = $true
    $form.NavigationButtons = $true
    
    # تعيين الخصائص العربية
    $form.RightToLeft = $true
    $form.BackColor = 16777215
    
    # حفظ النموذج
    $access.DoCmd.Save(2, "نموذج_إدارة_المواد")
    $access.DoCmd.Close(2, "نموذج_إدارة_المواد")

    Write-Host "تم إنشاء نموذج إدارة المواد" -ForegroundColor Green

    # إنشاء نموذج إدارة المخزون
    Write-Host "إنشاء نموذج إدارة المخزون..." -ForegroundColor Cyan

    $form = $access.CreateForm("أرصدة_المخزون")
    $form.Caption = "إدارة أرصدة المخزون"
    $form.DefaultView = 2 # Continuous Forms
    $form.AllowAdditions = $false
    $form.AllowDeletions = $false
    $form.AllowEdits = $true
    $form.DataEntry = $false
    $form.RecordSelectors = $true
    $form.NavigationButtons = $true

    # تعيين الخصائص العربية
    $form.RightToLeft = $true
    $form.BackColor = 16777215

    # حفظ النموذج
    $access.DoCmd.Save(2, "نموذج_إدارة_المخزون")
    $access.DoCmd.Close(2, "نموذج_إدارة_المخزون")

    Write-Host "تم إنشاء نموذج إدارة المخزون" -ForegroundColor Green

    # إنشاء نموذج إدارة الوصفات
    Write-Host "إنشاء نموذج إدارة الوصفات..." -ForegroundColor Cyan

    $form = $access.CreateForm("الوصفات")
    $form.Caption = "إدارة وصفات الإنتاج"
    $form.DefaultView = 0 # Single Form
    $form.AllowAdditions = $true
    $form.AllowDeletions = $true
    $form.AllowEdits = $true
    $form.DataEntry = $false
    $form.RecordSelectors = $true
    $form.NavigationButtons = $true

    # تعيين الخصائص العربية
    $form.RightToLeft = $true
    $form.BackColor = 16777215

    # حفظ النموذج
    $access.DoCmd.Save(2, "نموذج_إدارة_الوصفات")
    $access.DoCmd.Close(2, "نموذج_إدارة_الوصفات")

    Write-Host "تم إنشاء نموذج إدارة الوصفات" -ForegroundColor Green

    # إنشاء نموذج مكونات الوصفة
    Write-Host "إنشاء نموذج مكونات الوصفة..." -ForegroundColor Cyan

    $form = $access.CreateForm("مكونات_الوصفة")
    $form.Caption = "مكونات الوصفة"
    $form.DefaultView = 1 # Datasheet
    $form.AllowAdditions = $true
    $form.AllowDeletions = $true
    $form.AllowEdits = $true
    $form.DataEntry = $false
    $form.RecordSelectors = $true
    $form.NavigationButtons = $true

    # تعيين الخصائص العربية
    $form.RightToLeft = $true
    $form.BackColor = 16777215

    # حفظ النموذج
    $access.DoCmd.Save(2, "نموذج_مكونات_الوصفة")
    $access.DoCmd.Close(2, "نموذج_مكونات_الوصفة")

    Write-Host "تم إنشاء نموذج مكونات الوصفة" -ForegroundColor Green

    # إنشاء نموذج إدارة الموردين
    Write-Host "إنشاء نموذج إدارة الموردين..." -ForegroundColor Cyan

    $form = $access.CreateForm("الموردين")
    $form.Caption = "إدارة الموردين"
    $form.DefaultView = 0 # Single Form
    $form.AllowAdditions = $true
    $form.AllowDeletions = $true
    $form.AllowEdits = $true
    $form.DataEntry = $false
    $form.RecordSelectors = $true
    $form.NavigationButtons = $true

    # تعيين الخصائص العربية
    $form.RightToLeft = $true
    $form.BackColor = 16777215

    # حفظ النموذج
    $access.DoCmd.Save(2, "نموذج_إدارة_الموردين")
    $access.DoCmd.Close(2, "نموذج_إدارة_الموردين")

    Write-Host "تم إنشاء نموذج إدارة الموردين" -ForegroundColor Green

    # إنشاء نموذج حركات المخزون
    Write-Host "إنشاء نموذج حركات المخزون..." -ForegroundColor Cyan

    $form = $access.CreateForm("حركات_المخزون")
    $form.Caption = "حركات المخزون"
    $form.DefaultView = 2 # Continuous Forms
    $form.AllowAdditions = $true
    $form.AllowDeletions = $false
    $form.AllowEdits = $false
    $form.DataEntry = $false
    $form.RecordSelectors = $true
    $form.NavigationButtons = $true
    $form.OrderBy = "تاريخ_الحركة DESC"

    # تعيين الخصائص العربية
    $form.RightToLeft = $true
    $form.BackColor = 16777215

    # حفظ النموذج
    $access.DoCmd.Save(2, "نموذج_حركات_المخزون")
    $access.DoCmd.Close(2, "نموذج_حركات_المخزون")

    Write-Host "تم إنشاء نموذج حركات المخزون" -ForegroundColor Green

    # إنشاء نموذج مراكز التكلفة
    Write-Host "إنشاء نموذج مراكز التكلفة..." -ForegroundColor Cyan

    $form = $access.CreateForm("مراكز_التكلفة")
    $form.Caption = "إدارة مراكز التكلفة"
    $form.DefaultView = 0 # Single Form
    $form.AllowAdditions = $true
    $form.AllowDeletions = $true
    $form.AllowEdits = $true
    $form.DataEntry = $false
    $form.RecordSelectors = $true
    $form.NavigationButtons = $true

    # تعيين الخصائص العربية
    $form.RightToLeft = $true
    $form.BackColor = 16777215

    # حفظ النموذج
    $access.DoCmd.Save(2, "نموذج_مراكز_التكلفة")
    $access.DoCmd.Close(2, "نموذج_مراكز_التكلفة")
    
    Write-Host "تم إنشاء نموذج مراكز التكلفة" -ForegroundColor Green
    
    Write-Host "تم إنشاء جميع النماذج الأساسية بنجاح!" -ForegroundColor Green
    
    # إغلاق Access
    $access.CloseCurrentDatabase()
    $access.Quit()
    
} catch {
    Write-Host "حدث خطأ: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($access) {
        try {
            $access.Quit()
        } catch {}
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}
