# سكريبت إنشاء نظام إدارة تصنيع المعمول الكامل مع العلاقات والاستعلامات والنماذج والتقارير
# Manufacturing Management System for Ma'amoul Production - Complete with Relationships, Queries, Forms, and Reports

Write-Host "بدء إنشاء نظام إدارة تصنيع المعمول الكامل..." -ForegroundColor Green

# حذف قاعدة البيانات الموجودة إن وجدت
$dbPath = "نظام_إدارة_تصنيع_المعمول.accdb"
if (Test-Path $dbPath) {
    Remove-Item $dbPath -Force
    Write-Host "تم حذف قاعدة البيانات الموجودة" -ForegroundColor Yellow
}

try {
    # إنشاء كائن DAO
    $dao = New-Object -ComObject DAO.DBEngine.120
    
    # إنشاء قاعدة البيانات مع دعم اللغة العربية
    $db = $dao.CreateDatabase($dbPath, ";LANGID=0x0401;CP=1256;COUNTRY=0")
    
    Write-Host "تم إنشاء قاعدة البيانات بنجاح" -ForegroundColor Green
    
    # إنشاء الجداول الأساسية
    Write-Host "إنشاء الجداول الأساسية..." -ForegroundColor Cyan
    
    # 1. جدول وحدات القياس
    $tableSQL = @"
CREATE TABLE وحدات_القياس (
    رقم_الوحدة AUTOINCREMENT PRIMARY KEY,
    اسم_الوحدة TEXT(50) NOT NULL,
    الرمز TEXT(10) NOT NULL,
    نوع_الوحدة TEXT(20) NOT NULL,
    معامل_التحويل DOUBLE DEFAULT 1,
    ملاحظات MEMO
);
"@
    $db.Execute($tableSQL)
    
    # 2. جدول المستودعات
    $tableSQL = @"
CREATE TABLE المستودعات (
    رقم_المستودع AUTOINCREMENT PRIMARY KEY,
    اسم_المستودع TEXT(100) NOT NULL,
    الموقع TEXT(200),
    المسؤول TEXT(100),
    الهاتف TEXT(20),
    نشط YESNO DEFAULT True,
    ملاحظات MEMO
);
"@
    $db.Execute($tableSQL)
    
    # 3. جدول فئات المواد
    $tableSQL = @"
CREATE TABLE فئات_المواد (
    رقم_الفئة AUTOINCREMENT PRIMARY KEY,
    اسم_الفئة TEXT(100) NOT NULL,
    الوصف MEMO,
    نشط YESNO DEFAULT True
);
"@
    $db.Execute($tableSQL)
    
    # 4. جدول الموردين
    $tableSQL = @"
CREATE TABLE الموردين (
    رقم_المورد AUTOINCREMENT PRIMARY KEY,
    اسم_المورد TEXT(200) NOT NULL,
    العنوان MEMO,
    الهاتف TEXT(20),
    البريد_الإلكتروني TEXT(100),
    شخص_الاتصال TEXT(100),
    شروط_الدفع TEXT(100),
    نشط YESNO DEFAULT True,
    ملاحظات MEMO
);
"@
    $db.Execute($tableSQL)
    
    # 5. جدول العملاء
    $tableSQL = @"
CREATE TABLE العملاء (
    رقم_العميل AUTOINCREMENT PRIMARY KEY,
    اسم_العميل TEXT(200) NOT NULL,
    العنوان MEMO,
    الهاتف TEXT(20),
    البريد_الإلكتروني TEXT(100),
    شخص_الاتصال TEXT(100),
    حد_الائتمان CURRENCY DEFAULT 0,
    نشط YESNO DEFAULT True,
    ملاحظات MEMO
);
"@
    $db.Execute($tableSQL)
    
    # 6. جدول المواد والمنتجات
    $tableSQL = @"
CREATE TABLE المواد_والمنتجات (
    رقم_المادة AUTOINCREMENT PRIMARY KEY,
    كود_المادة TEXT(50) NOT NULL,
    اسم_المادة TEXT(200) NOT NULL,
    رقم_الفئة LONG NOT NULL,
    وحدة_القياس_الأساسية LONG NOT NULL,
    التكلفة_المعيارية CURRENCY DEFAULT 0,
    سعر_البيع CURRENCY DEFAULT 0,
    الحد_الأدنى_للمخزون DOUBLE DEFAULT 0,
    الحد_الأقصى_للمخزون DOUBLE DEFAULT 0,
    نقطة_إعادة_الطلب DOUBLE DEFAULT 0,
    مدة_الصلاحية_بالأيام LONG DEFAULT 0,
    نوع_المادة TEXT(20) DEFAULT 'مادة خام',
    نشط YESNO DEFAULT True,
    ملاحظات MEMO
);
"@
    $db.Execute($tableSQL)
    
    Write-Host "تم إنشاء الجداول الأساسية بنجاح" -ForegroundColor Green
    
    # 7. جدول أرصدة المخزون
    $tableSQL = @"
CREATE TABLE أرصدة_المخزون (
    رقم_الرصيد AUTOINCREMENT PRIMARY KEY,
    رقم_المادة LONG NOT NULL,
    رقم_المستودع LONG NOT NULL,
    رقم_الدفعة TEXT(50),
    تاريخ_الإنتاج DATETIME,
    تاريخ_انتهاء_الصلاحية DATETIME,
    الكمية_المتاحة DOUBLE DEFAULT 0,
    التكلفة_الوحدة CURRENCY DEFAULT 0,
    تاريخ_آخر_تحديث DATETIME DEFAULT Now()
);
"@
    $db.Execute($tableSQL)
    
    # 8. جدول حركات المخزون
    $tableSQL = @"
CREATE TABLE حركات_المخزون (
    رقم_الحركة AUTOINCREMENT PRIMARY KEY,
    رقم_المادة LONG NOT NULL,
    رقم_المستودع LONG NOT NULL,
    نوع_الحركة TEXT(20) NOT NULL,
    رقم_المرجع TEXT(50),
    تاريخ_الحركة DATETIME DEFAULT Now(),
    الكمية DOUBLE NOT NULL,
    التكلفة_الوحدة CURRENCY DEFAULT 0,
    إجمالي_التكلفة CURRENCY DEFAULT 0,
    رقم_الدفعة TEXT(50),
    تاريخ_انتهاء_الصلاحية DATETIME,
    المستخدم TEXT(50),
    ملاحظات MEMO
);
"@
    $db.Execute($tableSQL)
    
    # 9. جدول مراكز التكلفة
    $tableSQL = @"
CREATE TABLE مراكز_التكلفة (
    رقم_مركز_التكلفة AUTOINCREMENT PRIMARY KEY,
    اسم_مركز_التكلفة TEXT(100) NOT NULL,
    نوع_المركز TEXT(50) NOT NULL,
    معدل_التكلفة_ساعة CURRENCY DEFAULT 0,
    نشط YESNO DEFAULT True,
    ملاحظات MEMO
);
"@
    $db.Execute($tableSQL)
    
    # 10. جدول الوصفات
    $tableSQL = @"
CREATE TABLE الوصفات (
    رقم_الوصفة AUTOINCREMENT PRIMARY KEY,
    رقم_المنتج LONG NOT NULL,
    اسم_الوصفة TEXT(200) NOT NULL,
    كمية_الإنتاج DOUBLE NOT NULL,
    وحدة_الإنتاج LONG NOT NULL,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    تاريخ_آخر_تعديل DATETIME DEFAULT Now(),
    نشط YESNO DEFAULT True,
    ملاحظات MEMO
);
"@
    $db.Execute($tableSQL)
    
    # 11. جدول مكونات الوصفة
    $tableSQL = @"
CREATE TABLE مكونات_الوصفة (
    رقم_المكون AUTOINCREMENT PRIMARY KEY,
    رقم_الوصفة LONG NOT NULL,
    رقم_المادة LONG NOT NULL,
    الكمية_المطلوبة DOUBLE NOT NULL,
    وحدة_القياس LONG NOT NULL,
    التكلفة_المعيارية CURRENCY DEFAULT 0,
    نسبة_الفاقد DOUBLE DEFAULT 0,
    ملاحظات MEMO
);
"@
    $db.Execute($tableSQL)
    
    # 12. جدول أوامر الإنتاج
    $tableSQL = @"
CREATE TABLE أوامر_الإنتاج (
    رقم_أمر_الإنتاج AUTOINCREMENT PRIMARY KEY,
    رقم_الوصفة LONG NOT NULL,
    كمية_الإنتاج_المطلوبة DOUBLE NOT NULL,
    تاريخ_الأمر DATETIME DEFAULT Now(),
    تاريخ_البدء_المخطط DATETIME,
    تاريخ_الانتهاء_المخطط DATETIME,
    تاريخ_البدء_الفعلي DATETIME,
    تاريخ_الانتهاء_الفعلي DATETIME,
    حالة_الأمر TEXT(20) DEFAULT 'مخطط',
    رقم_المستودع LONG NOT NULL,
    التكلفة_المخططة CURRENCY DEFAULT 0,
    التكلفة_الفعلية CURRENCY DEFAULT 0,
    المسؤول TEXT(100),
    ملاحظات MEMO
);
"@
    $db.Execute($tableSQL)
    
    Write-Host "تم إنشاء جداول التصنيع بنجاح" -ForegroundColor Green
    
    # إنشاء الفهارس
    Write-Host "إنشاء الفهارس..." -ForegroundColor Cyan
    
    $db.Execute("CREATE INDEX IX_المواد_كود ON المواد_والمنتجات(كود_المادة)")
    $db.Execute("CREATE INDEX IX_المواد_اسم ON المواد_والمنتجات(اسم_المادة)")
    $db.Execute("CREATE INDEX IX_أرصدة_مادة_مستودع ON أرصدة_المخزون(رقم_المادة, رقم_المستودع)")
    $db.Execute("CREATE INDEX IX_حركات_مادة_تاريخ ON حركات_المخزون(رقم_المادة, تاريخ_الحركة)")
    $db.Execute("CREATE INDEX IX_مكونات_وصفة ON مكونات_الوصفة(رقم_الوصفة)")
    
    Write-Host "تم إنشاء الفهارس بنجاح" -ForegroundColor Green
    
    # إدراج البيانات الأساسية
    Write-Host "إدراج البيانات الأساسية..." -ForegroundColor Cyan
    
    # وحدات القياس
    $db.Execute("INSERT INTO وحدات_القياس (اسم_الوحدة, الرمز, نوع_الوحدة, معامل_التحويل) VALUES ('كيلوجرام', 'كجم', 'وزن', 1)")
    $db.Execute("INSERT INTO وحدات_القياس (اسم_الوحدة, الرمز, نوع_الوحدة, معامل_التحويل) VALUES ('جرام', 'جم', 'وزن', 0.001)")
    $db.Execute("INSERT INTO وحدات_القياس (اسم_الوحدة, الرمز, نوع_الوحدة, معامل_التحويل) VALUES ('قطعة', 'قطعة', 'عدد', 1)")
    $db.Execute("INSERT INTO وحدات_القياس (اسم_الوحدة, الرمز, نوع_الوحدة, معامل_التحويل) VALUES ('علبة', 'علبة', 'تعبئة', 1)")
    $db.Execute("INSERT INTO وحدات_القياس (اسم_الوحدة, الرمز, نوع_الوحدة, معامل_التحويل) VALUES ('لتر', 'لتر', 'حجم', 1)")
    
    # المستودعات
    $db.Execute("INSERT INTO المستودعات (اسم_المستودع, الموقع, المسؤول) VALUES ('مستودع المواد الخام', 'الطابق الأرضي - قسم أ', 'أحمد محمد')")
    $db.Execute("INSERT INTO المستودعات (اسم_المستودع, الموقع, المسؤول) VALUES ('مستودع المنتجات النهائية', 'الطابق الأول - قسم ب', 'فاطمة علي')")
    $db.Execute("INSERT INTO المستودعات (اسم_المستودع, الموقع, المسؤول) VALUES ('مستودع التعبئة والتغليف', 'الطابق الأرضي - قسم ج', 'محمد حسن')")
    
    # فئات المواد
    $db.Execute("INSERT INTO فئات_المواد (اسم_الفئة, الوصف) VALUES ('مكسرات', 'جميع أنواع المكسرات المستخدمة في الإنتاج')")
    $db.Execute("INSERT INTO فئات_المواد (اسم_الفئة, الوصف) VALUES ('دقيق ونشويات', 'الدقيق والنشا والمواد النشوية')")
    $db.Execute("INSERT INTO فئات_المواد (اسم_الفئة, الوصف) VALUES ('سكريات ومحليات', 'السكر والعسل والمحليات الطبيعية')")
    $db.Execute("INSERT INTO فئات_المواد (اسم_الفئة, الوصف) VALUES ('زيوت ودهون', 'الزيوت والزبدة والدهون المستخدمة')")
    $db.Execute("INSERT INTO فئات_المواد (اسم_الفئة, الوصف) VALUES ('توابل ونكهات', 'التوابل والنكهات والمواد المحسنة')")
    $db.Execute("INSERT INTO فئات_المواد (اسم_الفئة, الوصف) VALUES ('مواد تعبئة وتغليف', 'العلب والأكياس ومواد التغليف')")
    $db.Execute("INSERT INTO فئات_المواد (اسم_الفئة, الوصف) VALUES ('منتجات نهائية', 'المنتجات النهائية الجاهزة للبيع')")
    
    Write-Host "تم إدراج البيانات الأساسية بنجاح" -ForegroundColor Green
    
    # إغلاق قاعدة البيانات
    $db.Close()
    
    Write-Host "تم إنشاء النظام الأساسي بنجاح!" -ForegroundColor Green

    # إغلاق قاعدة البيانات مؤقتاً لإنشاء العلاقات
    $db.Close()

    # إعادة فتح قاعدة البيانات لإنشاء العلاقات والاستعلامات
    Write-Host "إنشاء العلاقات بين الجداول..." -ForegroundColor Cyan

    # فتح Access لإنشاء العلاقات والنماذج والتقارير
    $access = New-Object -ComObject Access.Application
    $access.OpenCurrentDatabase($dbPath)
    $access.Visible = $false

    # إنشاء العلاقات بين الجداول
    try {
        # علاقة المواد والمنتجات مع فئات المواد
        $access.CurrentDb.CreateRelation("FK_المواد_الفئة", "فئات_المواد", "المواد_والمنتجات", 4096)
        $rel = $access.CurrentDb.Relations("FK_المواد_الفئة")
        $rel.Fields.Append($rel.CreateField("رقم_الفئة"))
        $rel.Fields("رقم_الفئة").ForeignName = "رقم_الفئة"
        $access.CurrentDb.Relations.Append($rel)

        # علاقة المواد والمنتجات مع وحدات القياس
        $access.CurrentDb.CreateRelation("FK_المواد_الوحدة", "وحدات_القياس", "المواد_والمنتجات", 4096)
        $rel = $access.CurrentDb.Relations("FK_المواد_الوحدة")
        $rel.Fields.Append($rel.CreateField("رقم_الوحدة"))
        $rel.Fields("رقم_الوحدة").ForeignName = "وحدة_القياس_الأساسية"
        $access.CurrentDb.Relations.Append($rel)

        # علاقة أرصدة المخزون مع المواد والمنتجات
        $access.CurrentDb.CreateRelation("FK_أرصدة_المواد", "المواد_والمنتجات", "أرصدة_المخزون", 4096)
        $rel = $access.CurrentDb.Relations("FK_أرصدة_المواد")
        $rel.Fields.Append($rel.CreateField("رقم_المادة"))
        $rel.Fields("رقم_المادة").ForeignName = "رقم_المادة"
        $access.CurrentDb.Relations.Append($rel)

        # علاقة أرصدة المخزون مع المستودعات
        $access.CurrentDb.CreateRelation("FK_أرصدة_المستودعات", "المستودعات", "أرصدة_المخزون", 4096)
        $rel = $access.CurrentDb.Relations("FK_أرصدة_المستودعات")
        $rel.Fields.Append($rel.CreateField("رقم_المستودع"))
        $rel.Fields("رقم_المستودع").ForeignName = "رقم_المستودع"
        $access.CurrentDb.Relations.Append($rel)

        # علاقة حركات المخزون مع المواد والمنتجات
        $access.CurrentDb.CreateRelation("FK_حركات_المواد", "المواد_والمنتجات", "حركات_المخزون", 4096)
        $rel = $access.CurrentDb.Relations("FK_حركات_المواد")
        $rel.Fields.Append($rel.CreateField("رقم_المادة"))
        $rel.Fields("رقم_المادة").ForeignName = "رقم_المادة"
        $access.CurrentDb.Relations.Append($rel)

        # علاقة حركات المخزون مع المستودعات
        $access.CurrentDb.CreateRelation("FK_حركات_المستودعات", "المستودعات", "حركات_المخزون", 4096)
        $rel = $access.CurrentDb.Relations("FK_حركات_المستودعات")
        $rel.Fields.Append($rel.CreateField("رقم_المستودع"))
        $rel.Fields("رقم_المستودع").ForeignName = "رقم_المستودع"
        $access.CurrentDb.Relations.Append($rel)

        # علاقة الوصفات مع المنتجات
        $access.CurrentDb.CreateRelation("FK_وصفات_المنتجات", "المواد_والمنتجات", "الوصفات", 4096)
        $rel = $access.CurrentDb.Relations("FK_وصفات_المنتجات")
        $rel.Fields.Append($rel.CreateField("رقم_المادة"))
        $rel.Fields("رقم_المادة").ForeignName = "رقم_المنتج"
        $access.CurrentDb.Relations.Append($rel)

        # علاقة مكونات الوصفة مع الوصفات
        $access.CurrentDb.CreateRelation("FK_مكونات_الوصفات", "الوصفات", "مكونات_الوصفة", 4096)
        $rel = $access.CurrentDb.Relations("FK_مكونات_الوصفات")
        $rel.Fields.Append($rel.CreateField("رقم_الوصفة"))
        $rel.Fields("رقم_الوصفة").ForeignName = "رقم_الوصفة"
        $access.CurrentDb.Relations.Append($rel)

        # علاقة مكونات الوصفة مع المواد
        $access.CurrentDb.CreateRelation("FK_مكونات_المواد", "المواد_والمنتجات", "مكونات_الوصفة", 4096)
        $rel = $access.CurrentDb.Relations("FK_مكونات_المواد")
        $rel.Fields.Append($rel.CreateField("رقم_المادة"))
        $rel.Fields("رقم_المادة").ForeignName = "رقم_المادة"
        $access.CurrentDb.Relations.Append($rel)

        # علاقة أوامر الإنتاج مع الوصفات
        $access.CurrentDb.CreateRelation("FK_أوامر_الوصفات", "الوصفات", "أوامر_الإنتاج", 4096)
        $rel = $access.CurrentDb.Relations("FK_أوامر_الوصفات")
        $rel.Fields.Append($rel.CreateField("رقم_الوصفة"))
        $rel.Fields("رقم_الوصفة").ForeignName = "رقم_الوصفة"
        $access.CurrentDb.Relations.Append($rel)

        Write-Host "تم إنشاء العلاقات بنجاح" -ForegroundColor Green

    } catch {
        Write-Host "تحذير: بعض العلاقات قد تكون موجودة مسبقاً" -ForegroundColor Yellow
    }

} catch {
    Write-Host "خطأ في إنشاء النظام: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # تنظيف الكائنات
    if ($access) {
        $access.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
    }
    if ($db) { [System.Runtime.Interopservices.Marshal]::ReleaseComObject($db) | Out-Null }
    if ($dao) { [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dao) | Out-Null }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}
