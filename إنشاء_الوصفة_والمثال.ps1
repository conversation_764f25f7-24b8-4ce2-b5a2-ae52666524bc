# سكريپت إنشاء الوصفة والمثال التطبيقي الكامل
# تاريخ الإنشاء: 2025-09-19

Write-Host "بدء إنشاء الوصفة والمثال التطبيقي..." -ForegroundColor Green

try {
    $dbPath = Join-Path (Get-Location) "نظام_إدارة_تصنيع_المعمول.accdb"
    
    # فتح قاعدة البيانات
    $dao = New-Object -ComObject DAO.DBEngine.120
    $db = $dao.OpenDatabase($dbPath)
    
    # إنشاء وصفة معمول الجوز والعسل
    Write-Host "إنشاء وصفة معمول الجوز والعسل..." -ForegroundColor Cyan
    $rs = $db.OpenRecordset("الوصفات")
    
    $rs.AddNew()
    $rs.Fields("رقم_المنتج").Value = 9 # معمول الجوز والعسل
    $rs.Fields("رقم_إصدار_الوصفة").Value = "1.0"
    $rs.Fields("اسم_الوصفة").Value = "وصفة معمول الجوز والعسل الفاخر"
    $rs.Fields("وصف_الوصفة").Value = "وصفة تقليدية لصنع معمول الجوز والعسل بجودة عالية - تنتج 100 قطعة بوزن 600 جرام للعبوة"
    $rs.Fields("كمية_الإنتاج").Value = 100 # 100 قطعة
    $rs.Fields("وحدة_الإنتاج").Value = 3 # قطعة
    $rs.Fields("مدة_الإنتاج_بالدقائق").Value = 480 # 8 ساعات
    $rs.Fields("عدد_العمال_المطلوب").Value = 4
    $rs.Fields("تكلفة_العمالة_للوحدة").Value = 8.00 # 8 جنيه للقطعة الواحدة
    $rs.Fields("التكاليف_الإضافية").Value = 200.00 # تكاليف إضافية للدفعة
    $rs.Fields("حالة_الوصفة").Value = "نشطة"
    $rs.Fields("ملاحظات").Value = "وصفة معتمدة من قسم الجودة - تتطلب دقة في الأوزان والأوقات"
    $rs.Fields("المستخدم").Value = "مدير الإنتاج"
    $rs.Update()
    
    $rs.Close()
    Write-Host "تم إنشاء الوصفة الأساسية" -ForegroundColor Green
    
    # إضافة مكونات الوصفة
    Write-Host "إضافة مكونات الوصفة..." -ForegroundColor Cyan
    $rs = $db.OpenRecordset("مكونات_الوصفة")
    
    # دقيق القمح - 30 كجم
    $rs.AddNew()
    $rs.Fields("رقم_الوصفة").Value = 1
    $rs.Fields("رقم_المادة").Value = 1 # دقيق القمح
    $rs.Fields("الكمية_المطلوبة").Value = 30.0
    $rs.Fields("وحدة_القياس").Value = 1 # كيلوجرام
    $rs.Fields("نسبة_الفاقد").Value = 2.0 # 2% فاقد
    $rs.Fields("التكلفة_المعيارية").Value = 8.50
    $rs.Fields("ترتيب_الإضافة").Value = 1
    $rs.Fields("مرحلة_الإضافة").Value = "تحضير العجين"
    $rs.Fields("إجباري").Value = $true
    $rs.Fields("ملاحظات").Value = "يُنخل الدقيق قبل الاستخدام"
    $rs.Update()
    
    # جوز مقشر - 8 كجم
    $rs.AddNew()
    $rs.Fields("رقم_الوصفة").Value = 1
    $rs.Fields("رقم_المادة").Value = 2 # جوز مقشر
    $rs.Fields("الكمية_المطلوبة").Value = 8.0
    $rs.Fields("وحدة_القياس").Value = 1 # كيلوجرام
    $rs.Fields("نسبة_الفاقد").Value = 1.0 # 1% فاقد
    $rs.Fields("التكلفة_المعيارية").Value = 45.00
    $rs.Fields("ترتيب_الإضافة").Value = 2
    $rs.Fields("مرحلة_الإضافة").Value = "تحضير الحشو"
    $rs.Fields("إجباري").Value = $true
    $rs.Fields("ملاحظات").Value = "يُفرم الجوز ناعماً ويُحمص قليلاً"
    $rs.Update()
    
    # عسل نحل - 5 كجم
    $rs.AddNew()
    $rs.Fields("رقم_الوصفة").Value = 1
    $rs.Fields("رقم_المادة").Value = 3 # عسل نحل
    $rs.Fields("الكمية_المطلوبة").Value = 5.0
    $rs.Fields("وحدة_القياس").Value = 1 # كيلوجرام
    $rs.Fields("نسبة_الفاقد").Value = 0.5 # 0.5% فاقد
    $rs.Fields("التكلفة_المعيارية").Value = 80.00
    $rs.Fields("ترتيب_الإضافة").Value = 3
    $rs.Fields("مرحلة_الإضافة").Value = "تحضير الحشو"
    $rs.Fields("إجباري").Value = $true
    $rs.Fields("ملاحظات").Value = "يُسخن العسل قليلاً لسهولة الخلط"
    $rs.Update()
    
    # زبدة طبيعية - 6 كجم
    $rs.AddNew()
    $rs.Fields("رقم_الوصفة").Value = 1
    $rs.Fields("رقم_المادة").Value = 4 # زبدة طبيعية
    $rs.Fields("الكمية_المطلوبة").Value = 6.0
    $rs.Fields("وحدة_القياس").Value = 1 # كيلوجرام
    $rs.Fields("نسبة_الفاقد").Value = 1.0 # 1% فاقد
    $rs.Fields("التكلفة_المعيارية").Value = 25.00
    $rs.Fields("ترتيب_الإضافة").Value = 4
    $rs.Fields("مرحلة_الإضافة").Value = "تحضير العجين"
    $rs.Fields("إجباري").Value = $true
    $rs.Fields("ملاحظات").Value = "تُترك الزبدة في درجة حرارة الغرفة قبل الاستخدام"
    $rs.Update()
    
    # سكر ناعم - 4 كجم
    $rs.AddNew()
    $rs.Fields("رقم_الوصفة").Value = 1
    $rs.Fields("رقم_المادة").Value = 5 # سكر ناعم
    $rs.Fields("الكمية_المطلوبة").Value = 4.0
    $rs.Fields("وحدة_القياس").Value = 1 # كيلوجرام
    $rs.Fields("نسبة_الفاقد").Value = 0.5 # 0.5% فاقد
    $rs.Fields("التكلفة_المعيارية").Value = 12.00
    $rs.Fields("ترتيب_الإضافة").Value = 5
    $rs.Fields("مرحلة_الإضافة").Value = "تحضير العجين"
    $rs.Fields("إجباري").Value = $true
    $rs.Fields("ملاحظات").Value = "يُنخل السكر مع الدقيق"
    $rs.Update()
    
    # بيكنج باودر - 100 جرام
    $rs.AddNew()
    $rs.Fields("رقم_الوصفة").Value = 1
    $rs.Fields("رقم_المادة").Value = 6 # بيكنج باودر
    $rs.Fields("الكمية_المطلوبة").Value = 100.0
    $rs.Fields("وحدة_القياس").Value = 2 # جرام
    $rs.Fields("نسبة_الفاقد").Value = 0.0 # لا يوجد فاقد
    $rs.Fields("التكلفة_المعيارية").Value = 0.015
    $rs.Fields("ترتيب_الإضافة").Value = 6
    $rs.Fields("مرحلة_الإضافة").Value = "تحضير العجين"
    $rs.Fields("إجباري").Value = $true
    $rs.Fields("ملاحظات").Value = "يُضاف مع الدقيق"
    $rs.Update()
    
    # ملح طعام - 50 جرام
    $rs.AddNew()
    $rs.Fields("رقم_الوصفة").Value = 1
    $rs.Fields("رقم_المادة").Value = 7 # ملح طعام
    $rs.Fields("الكمية_المطلوبة").Value = 50.0
    $rs.Fields("وحدة_القياس").Value = 2 # جرام
    $rs.Fields("نسبة_الفاقد").Value = 0.0 # لا يوجد فاقد
    $rs.Fields("التكلفة_المعيارية").Value = 0.003
    $rs.Fields("ترتيب_الإضافة").Value = 7
    $rs.Fields("مرحلة_الإضافة").Value = "تحضير العجين"
    $rs.Fields("إجباري").Value = $true
    $rs.Fields("ملاحظات").Value = "يُضاف مع الدقيق"
    $rs.Update()
    
    # علب كرتونية - 100 قطعة
    $rs.AddNew()
    $rs.Fields("رقم_الوصفة").Value = 1
    $rs.Fields("رقم_المادة").Value = 8 # علب كرتونية
    $rs.Fields("الكمية_المطلوبة").Value = 100.0
    $rs.Fields("وحدة_القياس").Value = 3 # قطعة
    $rs.Fields("نسبة_الفاقد").Value = 2.0 # 2% فاقد في التعبئة
    $rs.Fields("التكلفة_المعيارية").Value = 2.50
    $rs.Fields("ترتيب_الإضافة").Value = 8
    $rs.Fields("مرحلة_الإضافة").Value = "التعبئة والتغليف"
    $rs.Fields("إجباري").Value = $true
    $rs.Fields("ملاحظات").Value = "تُستخدم في التعبئة النهائية"
    $rs.Update()
    
    $rs.Close()
    Write-Host "تم إضافة جميع مكونات الوصفة" -ForegroundColor Green
    
    # حساب التكلفة الإجمالية للوصفة
    Write-Host "حساب التكلفة الإجمالية للوصفة..." -ForegroundColor Cyan
    
    # تكلفة المواد الخام
    $تكلفة_دقيق = 30.0 * 8.50
    $تكلفة_جوز = 8.0 * 45.00
    $تكلفة_عسل = 5.0 * 80.00
    $تكلفة_زبدة = 6.0 * 25.00
    $تكلفة_سكر = 4.0 * 12.00
    $تكلفة_بيكنج = 100.0 * 0.015
    $تكلفة_ملح = 50.0 * 0.003
    $تكلفة_علب = 100.0 * 2.50
    
    $إجمالي_تكلفة_المواد = $تكلفة_دقيق + $تكلفة_جوز + $تكلفة_عسل + $تكلفة_زبدة + $تكلفة_سكر + $تكلفة_بيكنج + $تكلفة_ملح + $تكلفة_علب
    
    # تكلفة العمالة
    $تكلفة_العمالة = 100.0 * 8.00
    
    # التكاليف الإضافية
    $التكاليف_الإضافية = 200.00
    
    # إجمالي التكلفة
    $إجمالي_التكلفة = $إجمالي_تكلفة_المواد + $تكلفة_العمالة + $التكاليف_الإضافية
    
    # تكلفة القطعة الواحدة
    $تكلفة_القطعة_الواحدة = $إجمالي_التكلفة / 100.0
    
    Write-Host "=== تحليل التكلفة ===" -ForegroundColor Yellow
    Write-Host "تكلفة المواد الخام: $إجمالي_تكلفة_المواد جنيه" -ForegroundColor White
    Write-Host "تكلفة العمالة: $تكلفة_العمالة جنيه" -ForegroundColor White
    Write-Host "التكاليف الإضافية: $التكاليف_الإضافية جنيه" -ForegroundColor White
    Write-Host "إجمالي التكلفة: $إجمالي_التكلفة جنيه" -ForegroundColor White
    Write-Host "تكلفة القطعة الواحدة: $تكلفة_القطعة_الواحدة جنيه" -ForegroundColor White
    Write-Host "سعر البيع المقترح: 85.00 جنيه" -ForegroundColor White
    $هامش_الربح = 85.00 - $تكلفة_القطعة_الواحدة
    $نسبة_الربح = ($هامش_الربح / $تكلفة_القطعة_الواحدة) * 100
    Write-Host "هامش الربح: $هامش_الربح جنيه" -ForegroundColor Green
    Write-Host "نسبة الربح: $([math]::Round($نسبة_الربح, 2))%" -ForegroundColor Green
    
    # تحديث التكلفة المعيارية للمنتج النهائي
    $rs = $db.OpenRecordset("SELECT * FROM المواد_والمنتجات WHERE كود_المادة = 'FIN001'")
    if (-not $rs.EOF) {
        $rs.Edit()
        $rs.Fields("التكلفة_المعيارية").Value = $تكلفة_القطعة_الواحدة
        $rs.Update()
        Write-Host "تم تحديث التكلفة المعيارية للمنتج النهائي" -ForegroundColor Green
    }
    $rs.Close()
    
    Write-Host "تم إنشاء الوصفة والمثال التطبيقي بنجاح!" -ForegroundColor Green
    Write-Host "الوصفة جاهزة لإنتاج 100 قطعة من معمول الجوز والعسل" -ForegroundColor Cyan
    
    # إغلاق قاعدة البيانات
    $db.Close()
    
} catch {
    Write-Host "حدث خطأ: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($rs) { try { $rs.Close() } catch {} }
    if ($db) { try { $db.Close() } catch {} }
    if ($dao) {
        try {
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dao) | Out-Null
        } catch {}
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}
