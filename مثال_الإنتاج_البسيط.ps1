# مثال بسيط لعملية الإنتاج في نظام إدارة تصنيع المعمول
# يوضح دورة الإنتاج الكاملة من الشراء إلى البيع

Write-Host "=== مثال تطبيقي: إنتاج معمول الجوز والعسل ===" -ForegroundColor Cyan
Write-Host "سيناريو: إنتاج 100 قطعة من معمول الجوز والعسل 600 جرام" -ForegroundColor White
Write-Host ""

try {
    # الانتظار قليلاً للتأكد من إغلاق أي اتصالات سابقة
    Start-Sleep -Seconds 2
    
    $dbPath = "نظام_إدارة_تصنيع_المعمول.accdb"
    
    # التحقق من وجود قاعدة البيانات
    if (-not (Test-Path $dbPath)) {
        Write-Host "لم يتم العثور على ملف قاعدة البيانات. سيتم إنشاؤها أولاً..." -ForegroundColor Yellow
        
        # تشغيل السكريبتات الأساسية فقط
        & ".\إنشاء_قاعدة_البيانات_محسن.ps1"
        & ".\إكمال_الجداول.ps1"
        & ".\إنشاء_جداول_التصنيع.ps1"
        & ".\إدراج_البيانات_الأساسية.ps1"
        & ".\إنشاء_الوصفة_والمثال.ps1"
        
        Write-Host "تم إنشاء قاعدة البيانات بنجاح" -ForegroundColor Green
        Write-Host ""
    }
    
    # إنشاء اتصال بقاعدة البيانات
    $dbEngine = New-Object -ComObject DAO.DBEngine.120
    $db = $dbEngine.OpenDatabase($dbPath)
    
    Write-Host "تم الاتصال بقاعدة البيانات بنجاح" -ForegroundColor Green
    Write-Host ""
    
    # الخطوة 1: عرض الوصفة المطلوبة
    Write-Host "=== الخطوة 1: عرض الوصفة المطلوبة ===" -ForegroundColor Yellow
    
    $rs = $db.OpenRecordset("SELECT * FROM مكونات_الوصفة WHERE رقم_الوصفة = 1")
    Write-Host "مكونات وصفة معمول الجوز والعسل (100 قطعة):" -ForegroundColor White
    
    while (-not $rs.EOF) {
        $materialId = $rs.Fields("رقم_المادة").Value
        $quantity = $rs.Fields("الكمية_المطلوبة").Value
        $unit = $rs.Fields("وحدة_القياس").Value
        
        # الحصول على اسم المادة
        $materialRs = $db.OpenRecordset("SELECT اسم_المادة FROM المواد_والمنتجات WHERE رقم_المادة = $materialId")
        $materialName = $materialRs.Fields("اسم_المادة").Value
        $materialRs.Close()
        
        # الحصول على اسم الوحدة
        $unitRs = $db.OpenRecordset("SELECT اسم_الوحدة FROM وحدات_القياس WHERE رقم_الوحدة = $unit")
        $unitName = $unitRs.Fields("اسم_الوحدة").Value
        $unitRs.Close()
        
        Write-Host "  - $materialName : $quantity $unitName" -ForegroundColor Cyan
        $rs.MoveNext()
    }
    $rs.Close()
    
    Write-Host ""
    
    # الخطوة 2: عرض أرصدة المخزون الحالية
    Write-Host "=== الخطوة 2: أرصدة المخزون الحالية ===" -ForegroundColor Yellow
    
    $rs = $db.OpenRecordset("SELECT ب.رقم_المادة, م.اسم_المادة, ب.الكمية_المتاحة, و.اسم_الوحدة, ب.متوسط_التكلفة FROM أرصدة_المخزون ب INNER JOIN المواد_والمنتجات م ON ب.رقم_المادة = م.رقم_المادة INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة WHERE ب.الكمية_المتاحة > 0")
    
    while (-not $rs.EOF) {
        $materialName = $rs.Fields("اسم_المادة").Value
        $quantity = $rs.Fields("الكمية_المتاحة").Value
        $unitName = $rs.Fields("اسم_الوحدة").Value
        $cost = $rs.Fields("متوسط_التكلفة").Value
        
        Write-Host "  - $materialName : $quantity $unitName (متوسط التكلفة: $cost جنيه)" -ForegroundColor Cyan
        $rs.MoveNext()
    }
    $rs.Close()
    
    Write-Host ""
    
    # الخطوة 3: بدء عملية الإنتاج - صرف المواد
    Write-Host "=== الخطوة 3: بدء عملية الإنتاج - صرف المواد ===" -ForegroundColor Yellow
    
    # صرف المواد حسب الوصفة
    $rs = $db.OpenRecordset("حركات_المخزون")
    
    # صرف دقيق قمح - 30 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 1
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "صادر إنتاج"
    $rs.Fields("الكمية").Value = 30.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 8.50
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PROD-001"
    $rs.Fields("ملاحظات").Value = "صرف للإنتاج - دفعة معمول الجوز والعسل"
    $rs.Fields("المستخدم").Value = "مدير الإنتاج"
    $rs.Update()
    
    # صرف جوز مقشر - 8 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 2
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "صادر إنتاج"
    $rs.Fields("الكمية").Value = 8.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 45.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PROD-001"
    $rs.Fields("ملاحظات").Value = "صرف للإنتاج - دفعة معمول الجوز والعسل"
    $rs.Fields("المستخدم").Value = "مدير الإنتاج"
    $rs.Update()
    
    # صرف عسل نحل - 5 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 3
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "صادر إنتاج"
    $rs.Fields("الكمية").Value = 5.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 80.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PROD-001"
    $rs.Fields("ملاحظات").Value = "صرف للإنتاج - دفعة معمول الجوز والعسل"
    $rs.Fields("المستخدم").Value = "مدير الإنتاج"
    $rs.Update()
    
    # صرف زبدة طبيعية - 6 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 4
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "صادر إنتاج"
    $rs.Fields("الكمية").Value = 6.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 25.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PROD-001"
    $rs.Fields("ملاحظات").Value = "صرف للإنتاج - دفعة معمول الجوز والعسل"
    $rs.Fields("المستخدم").Value = "مدير الإنتاج"
    $rs.Update()
    
    # صرف سكر ناعم - 4 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 5
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "صادر إنتاج"
    $rs.Fields("الكمية").Value = 4.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 12.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PROD-001"
    $rs.Fields("ملاحظات").Value = "صرف للإنتاج - دفعة معمول الجوز والعسل"
    $rs.Fields("المستخدم").Value = "مدير الإنتاج"
    $rs.Update()
    
    # صرف علب كرتونية - 100 قطعة
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 8
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "صادر إنتاج"
    $rs.Fields("الكمية").Value = 100.0
    $rs.Fields("وحدة_القياس").Value = 3
    $rs.Fields("تكلفة_الوحدة").Value = 2.50
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PROD-001"
    $rs.Fields("ملاحظات").Value = "صرف للتعبئة - دفعة معمول الجوز والعسل"
    $rs.Fields("المستخدم").Value = "مدير الإنتاج"
    $rs.Update()
    
    $rs.Close()
    Write-Host "تم صرف المواد الخام للإنتاج" -ForegroundColor Green
    
    # الخطوة 4: إنتاج المنتج النهائي
    Write-Host "=== الخطوة 4: إنتاج المنتج النهائي ===" -ForegroundColor Yellow
    
    # تسجيل إنتاج المنتج النهائي
    $rs = $db.OpenRecordset("حركات_المخزون")
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 9
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "وارد إنتاج"
    $rs.Fields("الكمية").Value = 100.0
    $rs.Fields("وحدة_القياس").Value = 3
    $rs.Fields("تكلفة_الوحدة").Value = 24.6465 # التكلفة المحسوبة
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PROD-001"
    $rs.Fields("ملاحظات").Value = "إنتاج 100 قطعة معمول الجوز والعسل 600 جرام"
    $rs.Fields("المستخدم").Value = "مدير الإنتاج"
    $rs.Update()
    $rs.Close()
    
    # إضافة رصيد المنتج النهائي
    $rs = $db.OpenRecordset("أرصدة_المخزون")
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 9
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 100.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 24.6465
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()
    $rs.Close()
    
    Write-Host "تم إنتاج 100 قطعة من معمول الجوز والعسل" -ForegroundColor Green
    
    # الخطوة 5: تحديث أرصدة المخزون
    Write-Host "=== الخطوة 5: تحديث أرصدة المخزون ===" -ForegroundColor Yellow
    
    # تحديث أرصدة المواد الخام بعد الصرف
    $rs = $db.OpenRecordset("SELECT * FROM أرصدة_المخزون WHERE رقم_المادة = 1") # دقيق
    if (-not $rs.EOF) {
        $rs.Edit()
        $rs.Fields("الكمية_المتاحة").Value = 470.0 # 500 - 30
        $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
        $rs.Update()
    }
    $rs.Close()
    
    $rs = $db.OpenRecordset("SELECT * FROM أرصدة_المخزون WHERE رقم_المادة = 2") # جوز
    if (-not $rs.EOF) {
        $rs.Edit()
        $rs.Fields("الكمية_المتاحة").Value = 92.0 # 100 - 8
        $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
        $rs.Update()
    }
    $rs.Close()
    
    $rs = $db.OpenRecordset("SELECT * FROM أرصدة_المخزون WHERE رقم_المادة = 3") # عسل
    if (-not $rs.EOF) {
        $rs.Edit()
        $rs.Fields("الكمية_المتاحة").Value = 45.0 # 50 - 5
        $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
        $rs.Update()
    }
    $rs.Close()
    
    $rs = $db.OpenRecordset("SELECT * FROM أرصدة_المخزون WHERE رقم_المادة = 4") # زبدة
    if (-not $rs.EOF) {
        $rs.Edit()
        $rs.Fields("الكمية_المتاحة").Value = 74.0 # 80 - 6
        $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
        $rs.Update()
    }
    $rs.Close()
    
    $rs = $db.OpenRecordset("SELECT * FROM أرصدة_المخزون WHERE رقم_المادة = 5") # سكر
    if (-not $rs.EOF) {
        $rs.Edit()
        $rs.Fields("الكمية_المتاحة").Value = 196.0 # 200 - 4
        $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
        $rs.Update()
    }
    $rs.Close()
    
    $rs = $db.OpenRecordset("SELECT * FROM أرصدة_المخزون WHERE رقم_المادة = 8") # علب
    if (-not $rs.EOF) {
        $rs.Edit()
        $rs.Fields("الكمية_المتاحة").Value = 1900.0 # 2000 - 100
        $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
        $rs.Update()
    }
    $rs.Close()
    
    Write-Host "تم تحديث أرصدة المخزون" -ForegroundColor Green
    
    # الخطوة 6: بيع جزء من الإنتاج
    Write-Host "=== الخطوة 6: بيع جزء من الإنتاج ===" -ForegroundColor Yellow
    
    # بيع 50 قطعة من المنتج النهائي
    $rs = $db.OpenRecordset("حركات_المخزون")
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 9
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "صادر بيع"
    $rs.Fields("الكمية").Value = 50.0
    $rs.Fields("وحدة_القياس").Value = 3
    $rs.Fields("تكلفة_الوحدة").Value = 24.6465
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "SALE-001"
    $rs.Fields("ملاحظات").Value = "بيع 50 قطعة معمول الجوز والعسل - سعر البيع 85 جنيه للقطعة"
    $rs.Fields("المستخدم").Value = "مدير المبيعات"
    $rs.Update()
    $rs.Close()
    
    # تحديث رصيد المنتج النهائي
    $rs = $db.OpenRecordset("SELECT * FROM أرصدة_المخزون WHERE رقم_المادة = 9")
    if (-not $rs.EOF) {
        $rs.Edit()
        $rs.Fields("الكمية_المتاحة").Value = 50.0 # 100 - 50
        $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
        $rs.Update()
    }
    $rs.Close()
    
    Write-Host "تم بيع 50 قطعة من المنتج النهائي" -ForegroundColor Green
    
    # الخطوة 7: تحليل الربحية
    Write-Host "=== الخطوة 7: تحليل الربحية ===" -ForegroundColor Yellow
    
    $totalCost = 50 * 24.6465  # تكلفة 50 قطعة
    $totalRevenue = 50 * 85.0  # إيرادات البيع
    $totalProfit = $totalRevenue - $totalCost
    $profitMargin = ($totalProfit / $totalRevenue) * 100
    
    Write-Host "تحليل ربحية بيع 50 قطعة:" -ForegroundColor White
    Write-Host "  إجمالي التكلفة: $([math]::Round($totalCost, 2)) جنيه" -ForegroundColor Cyan
    Write-Host "  إجمالي الإيرادات: $([math]::Round($totalRevenue, 2)) جنيه" -ForegroundColor Cyan
    Write-Host "  صافي الربح: $([math]::Round($totalProfit, 2)) جنيه" -ForegroundColor Green
    Write-Host "  هامش الربح: $([math]::Round($profitMargin, 2))%" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "=== عرض الأرصدة النهائية ===" -ForegroundColor Yellow
    
    $rs = $db.OpenRecordset("SELECT ب.رقم_المادة, م.اسم_المادة, ب.الكمية_المتاحة, و.اسم_الوحدة FROM أرصدة_المخزون ب INNER JOIN المواد_والمنتجات م ON ب.رقم_المادة = م.رقم_المادة INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة WHERE ب.الكمية_المتاحة > 0")
    
    while (-not $rs.EOF) {
        $materialName = $rs.Fields("اسم_المادة").Value
        $quantity = $rs.Fields("الكمية_المتاحة").Value
        $unitName = $rs.Fields("اسم_الوحدة").Value
        
        Write-Host "  - $materialName : $quantity $unitName" -ForegroundColor Cyan
        $rs.MoveNext()
    }
    $rs.Close()
    
    Write-Host ""
    Write-Host "=== تم إكمال المثال التطبيقي بنجاح! ===" -ForegroundColor Green
    Write-Host "يمكنك الآن فتح ملف قاعدة البيانات: نظام_إدارة_تصنيع_المعمول.accdb" -ForegroundColor Cyan
    Write-Host "لاستعراض جميع البيانات والتقارير" -ForegroundColor Cyan
    
    # إغلاق الاتصال
    $db.Close()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($db) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dbEngine) | Out-Null
    
} catch {
    Write-Host "حدث خطأ: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
