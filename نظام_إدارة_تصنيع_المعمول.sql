-- نظام إدارة تصنيع معمول الجوز - Microsoft Access
-- تصميم قاعدة البيانات الشاملة
-- تاريخ الإنشاء: 2025-09-19

-- ========================================
-- جدول وحدات القياس
-- ========================================
CREATE TABLE وحدات_القياس (
    رقم_الوحدة AUTOINCREMENT PRIMARY KEY,
    اسم_الوحدة TEXT(50) NOT NULL,
    اختصار_الوحدة TEXT(10) NOT NULL,
    نوع_الوحدة TEXT(20) NOT NULL, -- وزن، حجم، عدد، طول
    معامل_التحويل_للوحدة_الأساسية DOUBLE DEFAULT 1,
    الوحدة_الأساسية YES/NO DEFAULT False,
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True
);

-- ========================================
-- جدول المستودعات
-- ========================================
CREATE TABLE المستودعات (
    رقم_المستودع AUTOINCREMENT PRIMARY KEY,
    اسم_المستودع TEXT(100) NOT NULL,
    موقع_المستودع TEXT(200),
    نوع_المستودع TEXT(50), -- مواد خام، منتجات نهائية، تحت التشغيل
    مسؤول_المستودع TEXT(100),
    هاتف_المسؤول TEXT(20),
    السعة_القصوى DOUBLE,
    وحدة_السعة TEXT(20),
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True
);

-- ========================================
-- جدول فئات المواد
-- ========================================
CREATE TABLE فئات_المواد (
    رقم_الفئة AUTOINCREMENT PRIMARY KEY,
    اسم_الفئة TEXT(100) NOT NULL,
    وصف_الفئة MEMO,
    فئة_رئيسية LONG, -- مرجع ذاتي للفئات الفرعية
    رمز_الفئة TEXT(20),
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True,
    FOREIGN KEY (فئة_رئيسية) REFERENCES فئات_المواد(رقم_الفئة)
);

-- ========================================
-- جدول المواد والمنتجات
-- ========================================
CREATE TABLE المواد_والمنتجات (
    رقم_المادة AUTOINCREMENT PRIMARY KEY,
    كود_المادة TEXT(50) NOT NULL UNIQUE,
    اسم_المادة TEXT(200) NOT NULL,
    وصف_المادة MEMO,
    رقم_الفئة LONG NOT NULL,
    نوع_المادة TEXT(50) NOT NULL, -- مادة خام، منتج نهائي، منتج وسطي، مادة تعبئة
    وحدة_القياس_الأساسية LONG NOT NULL,
    وحدة_الشراء LONG,
    وحدة_البيع LONG,
    وحدة_الإنتاج LONG,
    الحد_الأدنى_للمخزون DOUBLE DEFAULT 0,
    الحد_الأقصى_للمخزون DOUBLE DEFAULT 0,
    نقطة_إعادة_الطلب DOUBLE DEFAULT 0,
    مدة_الصلاحية_بالأيام LONG DEFAULT 0,
    يتطلب_تتبع_الدفعات YES/NO DEFAULT False,
    يتطلب_تتبع_الصلاحية YES/NO DEFAULT False,
    التكلفة_المعيارية CURRENCY DEFAULT 0,
    سعر_البيع_المقترح CURRENCY DEFAULT 0,
    معدل_الضريبة DOUBLE DEFAULT 0,
    ملاحظات MEMO,
    صورة_المادة OLEOBJECT,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    تاريخ_آخر_تحديث DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True,
    FOREIGN KEY (رقم_الفئة) REFERENCES فئات_المواد(رقم_الفئة),
    FOREIGN KEY (وحدة_القياس_الأساسية) REFERENCES وحدات_القياس(رقم_الوحدة),
    FOREIGN KEY (وحدة_الشراء) REFERENCES وحدات_القياس(رقم_الوحدة),
    FOREIGN KEY (وحدة_البيع) REFERENCES وحدات_القياس(رقم_الوحدة),
    FOREIGN KEY (وحدة_الإنتاج) REFERENCES وحدات_القياس(رقم_الوحدة)
);

-- ========================================
-- جدول الموردين
-- ========================================
CREATE TABLE الموردين (
    رقم_المورد AUTOINCREMENT PRIMARY KEY,
    كود_المورد TEXT(50) NOT NULL UNIQUE,
    اسم_المورد TEXT(200) NOT NULL,
    اسم_جهة_الاتصال TEXT(100),
    العنوان MEMO,
    المدينة TEXT(100),
    المحافظة TEXT(100),
    الرمز_البريدي TEXT(20),
    الهاتف TEXT(50),
    الفاكس TEXT(50),
    البريد_الإلكتروني TEXT(100),
    الموقع_الإلكتروني TEXT(200),
    شروط_الدفع TEXT(100),
    مدة_التوريد_بالأيام LONG DEFAULT 0,
    حد_الائتمان CURRENCY DEFAULT 0,
    معدل_الخصم DOUBLE DEFAULT 0,
    تقييم_المورد TEXT(20), -- ممتاز، جيد، مقبول، ضعيف
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    تاريخ_آخر_تحديث DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True
);

-- ========================================
-- جدول العملاء
-- ========================================
CREATE TABLE العملاء (
    رقم_العميل AUTOINCREMENT PRIMARY KEY,
    كود_العميل TEXT(50) NOT NULL UNIQUE,
    اسم_العميل TEXT(200) NOT NULL,
    نوع_العميل TEXT(50), -- فرد، شركة، موزع، تاجر تجزئة
    اسم_جهة_الاتصال TEXT(100),
    العنوان MEMO,
    المدينة TEXT(100),
    المحافظة TEXT(100),
    الرمز_البريدي TEXT(20),
    الهاتف TEXT(50),
    الفاكس TEXT(50),
    البريد_الإلكتروني TEXT(100),
    الموقع_الإلكتروني TEXT(200),
    شروط_الدفع TEXT(100),
    حد_الائتمان CURRENCY DEFAULT 0,
    معدل_الخصم DOUBLE DEFAULT 0,
    تصنيف_العميل TEXT(20), -- A، B، C
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    تاريخ_آخر_تحديث DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True
);

-- ========================================
-- جدول أرصدة المخزون
-- ========================================
CREATE TABLE أرصدة_المخزون (
    رقم_الرصيد AUTOINCREMENT PRIMARY KEY,
    رقم_المادة LONG NOT NULL,
    رقم_المستودع LONG NOT NULL,
    رقم_الدفعة TEXT(50),
    تاريخ_الصلاحية DATE,
    الكمية_المتاحة DOUBLE DEFAULT 0,
    الكمية_المحجوزة DOUBLE DEFAULT 0,
    الكمية_المطلوبة DOUBLE DEFAULT 0,
    التكلفة_الوحدة CURRENCY DEFAULT 0,
    إجمالي_القيمة CURRENCY DEFAULT 0,
    تاريخ_آخر_حركة DATETIME DEFAULT Now(),
    ملاحظات MEMO,
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_والمنتجات(رقم_المادة),
    FOREIGN KEY (رقم_المستودع) REFERENCES المستودعات(رقم_المستودع),
    UNIQUE (رقم_المادة, رقم_المستودع, رقم_الدفعة)
);

-- ========================================
-- جدول حركات المخزون
-- ========================================
CREATE TABLE حركات_المخزون (
    رقم_الحركة AUTOINCREMENT PRIMARY KEY,
    رقم_المادة LONG NOT NULL,
    رقم_المستودع LONG NOT NULL,
    نوع_الحركة TEXT(50) NOT NULL, -- استلام، صرف، تحويل، تسوية، إنتاج
    رقم_المستند TEXT(50),
    تاريخ_الحركة DATETIME DEFAULT Now(),
    رقم_الدفعة TEXT(50),
    تاريخ_الصلاحية DATE,
    الكمية DOUBLE NOT NULL,
    تكلفة_الوحدة CURRENCY DEFAULT 0,
    إجمالي_التكلفة CURRENCY DEFAULT 0,
    الرصيد_بعد_الحركة DOUBLE DEFAULT 0,
    متوسط_التكلفة_بعد_الحركة CURRENCY DEFAULT 0,
    مرجع_المستند TEXT(100),
    ملاحظات MEMO,
    المستخدم TEXT(50),
    تاريخ_الإدخال DATETIME DEFAULT Now(),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_والمنتجات(رقم_المادة),
    FOREIGN KEY (رقم_المستودع) REFERENCES المستودعات(رقم_المستودع)
);

-- ========================================
-- جدول الوصفات (Bill of Materials)
-- ========================================
CREATE TABLE الوصفات (
    رقم_الوصفة AUTOINCREMENT PRIMARY KEY,
    رقم_المنتج LONG NOT NULL,
    رقم_إصدار_الوصفة TEXT(20) DEFAULT "1.0",
    اسم_الوصفة TEXT(200) NOT NULL,
    وصف_الوصفة MEMO,
    كمية_الإنتاج DOUBLE NOT NULL DEFAULT 1,
    وحدة_الإنتاج LONG NOT NULL,
    مدة_الإنتاج_بالدقائق LONG DEFAULT 0,
    عدد_العمال_المطلوب LONG DEFAULT 1,
    تكلفة_العمالة_للوحدة CURRENCY DEFAULT 0,
    التكاليف_الإضافية CURRENCY DEFAULT 0,
    تاريخ_بداية_الصلاحية DATE DEFAULT Date(),
    تاريخ_نهاية_الصلاحية DATE,
    حالة_الوصفة TEXT(20) DEFAULT "نشطة", -- نشطة، معلقة، ملغاة
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    تاريخ_آخر_تحديث DATETIME DEFAULT Now(),
    المستخدم TEXT(50),
    FOREIGN KEY (رقم_المنتج) REFERENCES المواد_والمنتجات(رقم_المادة),
    FOREIGN KEY (وحدة_الإنتاج) REFERENCES وحدات_القياس(رقم_الوحدة)
);

-- ========================================
-- جدول مكونات الوصفة
-- ========================================
CREATE TABLE مكونات_الوصفة (
    رقم_المكون AUTOINCREMENT PRIMARY KEY,
    رقم_الوصفة LONG NOT NULL,
    رقم_المادة LONG NOT NULL,
    الكمية_المطلوبة DOUBLE NOT NULL,
    وحدة_القياس LONG NOT NULL,
    نسبة_الفاقد DOUBLE DEFAULT 0, -- نسبة مئوية
    التكلفة_المعيارية CURRENCY DEFAULT 0,
    ملاحظات MEMO,
    ترتيب_الإضافة LONG DEFAULT 1,
    مرحلة_الإضافة TEXT(50), -- تحضير، خلط، تشكيل، خبز، تبريد، تعبئة
    إجباري YES/NO DEFAULT True,
    FOREIGN KEY (رقم_الوصفة) REFERENCES الوصفات(رقم_الوصفة),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_والمنتجات(رقم_المادة),
    FOREIGN KEY (وحدة_القياس) REFERENCES وحدات_القياس(رقم_الوحدة)
);

-- ========================================
-- جدول مراكز التكلفة
-- ========================================
CREATE TABLE مراكز_التكلفة (
    رقم_مركز_التكلفة AUTOINCREMENT PRIMARY KEY,
    كود_مركز_التكلفة TEXT(50) NOT NULL UNIQUE,
    اسم_مركز_التكلفة TEXT(200) NOT NULL,
    نوع_مركز_التكلفة TEXT(50), -- إنتاج، خدمات، إداري
    وصف_المركز MEMO,
    معدل_التحميل_للساعة CURRENCY DEFAULT 0,
    التكاليف_الثابتة_الشهرية CURRENCY DEFAULT 0,
    السعة_الإنتاجية_الشهرية DOUBLE DEFAULT 0,
    وحدة_السعة TEXT(50),
    المسؤول TEXT(100),
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True
);

-- ========================================
-- جدول أوامر الإنتاج
-- ========================================
CREATE TABLE أوامر_الإنتاج (
    رقم_أمر_الإنتاج AUTOINCREMENT PRIMARY KEY,
    رقم_الأمر TEXT(50) NOT NULL UNIQUE,
    رقم_المنتج LONG NOT NULL,
    رقم_الوصفة LONG NOT NULL,
    الكمية_المطلوبة DOUBLE NOT NULL,
    الكمية_المنتجة DOUBLE DEFAULT 0,
    الكمية_المعيبة DOUBLE DEFAULT 0,
    تاريخ_الأمر DATE DEFAULT Date(),
    تاريخ_البداية_المخطط DATE,
    تاريخ_الانتهاء_المخطط DATE,
    تاريخ_البداية_الفعلي DATE,
    تاريخ_الانتهاء_الفعلي DATE,
    حالة_الأمر TEXT(20) DEFAULT "مخطط", -- مخطط، قيد التنفيذ، مكتمل، ملغى، معلق
    أولوية_الأمر TEXT(20) DEFAULT "عادية", -- عالية، عادية، منخفضة
    رقم_مركز_التكلفة LONG,
    رقم_المستودع_الإنتاج LONG,
    رقم_المستودع_المنتج_النهائي LONG,
    التكلفة_المخططة CURRENCY DEFAULT 0,
    التكلفة_الفعلية CURRENCY DEFAULT 0,
    ملاحظات MEMO,
    المستخدم TEXT(50),
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    تاريخ_آخر_تحديث DATETIME DEFAULT Now(),
    FOREIGN KEY (رقم_المنتج) REFERENCES المواد_والمنتجات(رقم_المادة),
    FOREIGN KEY (رقم_الوصفة) REFERENCES الوصفات(رقم_الوصفة),
    FOREIGN KEY (رقم_مركز_التكلفة) REFERENCES مراكز_التكلفة(رقم_مركز_التكلفة),
    FOREIGN KEY (رقم_المستودع_الإنتاج) REFERENCES المستودعات(رقم_المستودع),
    FOREIGN KEY (رقم_المستودع_المنتج_النهائي) REFERENCES المستودعات(رقم_المستودع)
);

-- ========================================
-- جدول تفاصيل استهلاك المواد في الإنتاج
-- ========================================
CREATE TABLE استهلاك_المواد_الإنتاج (
    رقم_الاستهلاك AUTOINCREMENT PRIMARY KEY,
    رقم_أمر_الإنتاج LONG NOT NULL,
    رقم_المادة LONG NOT NULL,
    الكمية_المخططة DOUBLE NOT NULL,
    الكمية_المستهلكة_فعلياً DOUBLE DEFAULT 0,
    تكلفة_الوحدة CURRENCY DEFAULT 0,
    إجمالي_التكلفة CURRENCY DEFAULT 0,
    تاريخ_الاستهلاك DATETIME DEFAULT Now(),
    رقم_الدفعة TEXT(50),
    ملاحظات MEMO,
    FOREIGN KEY (رقم_أمر_الإنتاج) REFERENCES أوامر_الإنتاج(رقم_أمر_الإنتاج),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_والمنتجات(رقم_المادة)
);

-- ========================================
-- جدول تكاليف العمالة
-- ========================================
CREATE TABLE تكاليف_العمالة (
    رقم_تكلفة_العمالة AUTOINCREMENT PRIMARY KEY,
    رقم_أمر_الإنتاج LONG NOT NULL,
    رقم_مركز_التكلفة LONG NOT NULL,
    نوع_العمالة TEXT(50), -- مباشرة، غير مباشرة، إشرافية
    عدد_الساعات DOUBLE DEFAULT 0,
    معدل_الأجر_للساعة CURRENCY DEFAULT 0,
    إجمالي_تكلفة_العمالة CURRENCY DEFAULT 0,
    تاريخ_العمل DATE DEFAULT Date(),
    وصف_العمل MEMO,
    اسم_العامل TEXT(100),
    ملاحظات MEMO,
    FOREIGN KEY (رقم_أمر_الإنتاج) REFERENCES أوامر_الإنتاج(رقم_أمر_الإنتاج),
    FOREIGN KEY (رقم_مركز_التكلفة) REFERENCES مراكز_التكلفة(رقم_مركز_التكلفة)
);

-- ========================================
-- جدول التكاليف الإضافية (Overhead)
-- ========================================
CREATE TABLE التكاليف_الإضافية (
    رقم_التكلفة_الإضافية AUTOINCREMENT PRIMARY KEY,
    رقم_أمر_الإنتاج LONG NOT NULL,
    رقم_مركز_التكلفة LONG NOT NULL,
    نوع_التكلفة TEXT(50), -- كهرباء، غاز، صيانة، إيجار، تأمين
    مبلغ_التكلفة CURRENCY DEFAULT 0,
    طريقة_التوزيع TEXT(50), -- ساعات عمل، كمية إنتاج، تكلفة مباشرة
    أساس_التوزيع DOUBLE DEFAULT 0,
    تاريخ_التكلفة DATE DEFAULT Date(),
    وصف_التكلفة MEMO,
    ملاحظات MEMO,
    FOREIGN KEY (رقم_أمر_الإنتاج) REFERENCES أوامر_الإنتاج(رقم_أمر_الإنتاج),
    FOREIGN KEY (رقم_مركز_التكلفة) REFERENCES مراكز_التكلفة(رقم_مركز_التكلفة)
);

-- ========================================
-- جدول أوامر الشراء
-- ========================================
CREATE TABLE أوامر_الشراء (
    رقم_أمر_الشراء AUTOINCREMENT PRIMARY KEY,
    رقم_الأمر TEXT(50) NOT NULL UNIQUE,
    رقم_المورد LONG NOT NULL,
    تاريخ_الأمر DATE DEFAULT Date(),
    تاريخ_التسليم_المطلوب DATE,
    تاريخ_التسليم_الفعلي DATE,
    حالة_الأمر TEXT(20) DEFAULT "مفتوح", -- مفتوح، مؤكد، مستلم جزئياً، مستلم كاملاً، ملغى
    إجمالي_الأمر_قبل_الضريبة CURRENCY DEFAULT 0,
    مبلغ_الضريبة CURRENCY DEFAULT 0,
    إجمالي_الأمر_بعد_الضريبة CURRENCY DEFAULT 0,
    مبلغ_الخصم CURRENCY DEFAULT 0,
    صافي_المبلغ CURRENCY DEFAULT 0,
    شروط_الدفع TEXT(200),
    ملاحظات MEMO,
    المستخدم TEXT(50),
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    تاريخ_آخر_تحديث DATETIME DEFAULT Now(),
    FOREIGN KEY (رقم_المورد) REFERENCES الموردين(رقم_المورد)
);

-- ========================================
-- جدول تفاصيل أوامر الشراء
-- ========================================
CREATE TABLE تفاصيل_أوامر_الشراء (
    رقم_التفصيل AUTOINCREMENT PRIMARY KEY,
    رقم_أمر_الشراء LONG NOT NULL,
    رقم_المادة LONG NOT NULL,
    الكمية_المطلوبة DOUBLE NOT NULL,
    الكمية_المستلمة DOUBLE DEFAULT 0,
    سعر_الوحدة CURRENCY NOT NULL,
    إجمالي_السطر CURRENCY DEFAULT 0,
    معدل_الضريبة DOUBLE DEFAULT 0,
    مبلغ_الضريبة CURRENCY DEFAULT 0,
    معدل_الخصم DOUBLE DEFAULT 0,
    مبلغ_الخصم CURRENCY DEFAULT 0,
    صافي_المبلغ CURRENCY DEFAULT 0,
    تاريخ_التسليم_المطلوب DATE,
    ملاحظات MEMO,
    FOREIGN KEY (رقم_أمر_الشراء) REFERENCES أوامر_الشراء(رقم_أمر_الشراء),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_والمنتجات(رقم_المادة)
);

-- ========================================
-- جدول استلام المواد
-- ========================================
CREATE TABLE استلام_المواد (
    رقم_الاستلام AUTOINCREMENT PRIMARY KEY,
    رقم_مستند_الاستلام TEXT(50) NOT NULL UNIQUE,
    رقم_أمر_الشراء LONG,
    رقم_المورد LONG NOT NULL,
    تاريخ_الاستلام DATE DEFAULT Date(),
    رقم_فاتورة_المورد TEXT(50),
    تاريخ_فاتورة_المورد DATE,
    رقم_المستودع LONG NOT NULL,
    إجمالي_قيمة_الاستلام CURRENCY DEFAULT 0,
    حالة_الاستلام TEXT(20) DEFAULT "مؤكد", -- مؤقت، مؤكد، ملغى
    ملاحظات MEMO,
    المستخدم TEXT(50),
    تاريخ_الإدخال DATETIME DEFAULT Now(),
    FOREIGN KEY (رقم_أمر_الشراء) REFERENCES أوامر_الشراء(رقم_أمر_الشراء),
    FOREIGN KEY (رقم_المورد) REFERENCES الموردين(رقم_المورد),
    FOREIGN KEY (رقم_المستودع) REFERENCES المستودعات(رقم_المستودع)
);

-- ========================================
-- جدول تفاصيل استلام المواد
-- ========================================
CREATE TABLE تفاصيل_استلام_المواد (
    رقم_تفصيل_الاستلام AUTOINCREMENT PRIMARY KEY,
    رقم_الاستلام LONG NOT NULL,
    رقم_المادة LONG NOT NULL,
    رقم_الدفعة TEXT(50),
    تاريخ_الإنتاج DATE,
    تاريخ_الصلاحية DATE,
    الكمية_المستلمة DOUBLE NOT NULL,
    سعر_الوحدة CURRENCY NOT NULL,
    إجمالي_التكلفة CURRENCY DEFAULT 0,
    حالة_الجودة TEXT(20) DEFAULT "مقبول", -- مقبول، مرفوض، يحتاج فحص
    ملاحظات_الجودة MEMO,
    ملاحظات MEMO,
    FOREIGN KEY (رقم_الاستلام) REFERENCES استلام_المواد(رقم_الاستلام),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_والمنتجات(رقم_المادة)
);

-- ========================================
-- جدول أوامر البيع
-- ========================================
CREATE TABLE أوامر_البيع (
    رقم_أمر_البيع AUTOINCREMENT PRIMARY KEY,
    رقم_الأمر TEXT(50) NOT NULL UNIQUE,
    رقم_العميل LONG NOT NULL,
    تاريخ_الأمر DATE DEFAULT Date(),
    تاريخ_التسليم_المطلوب DATE,
    تاريخ_التسليم_الفعلي DATE,
    حالة_الأمر TEXT(20) DEFAULT "مفتوح", -- مفتوح، مؤكد، قيد التحضير، جاهز للشحن، مشحون، مكتمل، ملغى
    إجمالي_الأمر_قبل_الضريبة CURRENCY DEFAULT 0,
    مبلغ_الضريبة CURRENCY DEFAULT 0,
    إجمالي_الأمر_بعد_الضريبة CURRENCY DEFAULT 0,
    مبلغ_الخصم CURRENCY DEFAULT 0,
    صافي_المبلغ CURRENCY DEFAULT 0,
    شروط_الدفع TEXT(200),
    عنوان_التسليم MEMO,
    ملاحظات MEMO,
    المستخدم TEXT(50),
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    تاريخ_آخر_تحديث DATETIME DEFAULT Now(),
    FOREIGN KEY (رقم_العميل) REFERENCES العملاء(رقم_العميل)
);

-- ========================================
-- جدول تفاصيل أوامر البيع
-- ========================================
CREATE TABLE تفاصيل_أوامر_البيع (
    رقم_التفصيل AUTOINCREMENT PRIMARY KEY,
    رقم_أمر_البيع LONG NOT NULL,
    رقم_المادة LONG NOT NULL,
    الكمية_المطلوبة DOUBLE NOT NULL,
    الكمية_المشحونة DOUBLE DEFAULT 0,
    سعر_الوحدة CURRENCY NOT NULL,
    إجمالي_السطر CURRENCY DEFAULT 0,
    معدل_الضريبة DOUBLE DEFAULT 0,
    مبلغ_الضريبة CURRENCY DEFAULT 0,
    معدل_الخصم DOUBLE DEFAULT 0,
    مبلغ_الخصم CURRENCY DEFAULT 0,
    صافي_المبلغ CURRENCY DEFAULT 0,
    تاريخ_التسليم_المطلوب DATE,
    ملاحظات MEMO,
    FOREIGN KEY (رقم_أمر_البيع) REFERENCES أوامر_البيع(رقم_أمر_البيع),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_والمنتجات(رقم_المادة)
);

-- ========================================
-- جدول إعدادات النظام
-- ========================================
CREATE TABLE إعدادات_النظام (
    رقم_الإعداد AUTOINCREMENT PRIMARY KEY,
    اسم_الإعداد TEXT(100) NOT NULL UNIQUE,
    قيمة_الإعداد TEXT(500),
    نوع_البيانات TEXT(20) DEFAULT "نص", -- نص، رقم، تاريخ، منطقي
    وصف_الإعداد MEMO,
    مجموعة_الإعدادات TEXT(50), -- عام، مخزون، إنتاج، محاسبة
    قابل_للتعديل YES/NO DEFAULT True,
    تاريخ_آخر_تحديث DATETIME DEFAULT Now(),
    المستخدم TEXT(50)
);

-- ========================================
-- جدول المستخدمين والصلاحيات
-- ========================================
CREATE TABLE المستخدمين (
    رقم_المستخدم AUTOINCREMENT PRIMARY KEY,
    اسم_المستخدم TEXT(50) NOT NULL UNIQUE,
    كلمة_المرور TEXT(100),
    الاسم_الكامل TEXT(200) NOT NULL,
    البريد_الإلكتروني TEXT(100),
    الهاتف TEXT(20),
    المسمى_الوظيفي TEXT(100),
    القسم TEXT(100),
    مستوى_الصلاحية TEXT(20) DEFAULT "مستخدم", -- مدير، مشرف، مستخدم، قارئ
    تاريخ_آخر_دخول DATETIME,
    نشط YES/NO DEFAULT True,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    تاريخ_آخر_تحديث DATETIME DEFAULT Now()
);

-- ========================================
-- جدول سجل العمليات (Audit Trail)
-- ========================================
CREATE TABLE سجل_العمليات (
    رقم_السجل AUTOINCREMENT PRIMARY KEY,
    اسم_المستخدم TEXT(50) NOT NULL,
    نوع_العملية TEXT(50) NOT NULL, -- إدخال، تعديل، حذف، استعلام
    اسم_الجدول TEXT(100),
    رقم_السجل_المتأثر LONG,
    تفاصيل_العملية MEMO,
    تاريخ_العملية DATETIME DEFAULT Now(),
    عنوان_IP TEXT(50),
    اسم_الكمبيوتر TEXT(100)
);

-- ========================================
-- جدول أرقام المستندات التلقائية
-- ========================================
CREATE TABLE أرقام_المستندات (
    رقم_المستند_التلقائي AUTOINCREMENT PRIMARY KEY,
    نوع_المستند TEXT(50) NOT NULL UNIQUE,
    البادئة TEXT(20),
    الرقم_الحالي LONG DEFAULT 1,
    طول_الرقم LONG DEFAULT 6,
    اللاحقة TEXT(20),
    تاريخ_آخر_استخدام DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True
);

-- ========================================
-- إدراج البيانات الأساسية
-- ========================================

-- إدراج وحدات القياس الأساسية
INSERT INTO وحدات_القياس (اسم_الوحدة, اختصار_الوحدة, نوع_الوحدة, معامل_التحويل_للوحدة_الأساسية, الوحدة_الأساسية)
VALUES
('كيلوجرام', 'كجم', 'وزن', 1, True),
('جرام', 'جم', 'وزن', 0.001, False),
('طن', 'طن', 'وزن', 1000, False),
('لتر', 'لتر', 'حجم', 1, True),
('مليلتر', 'مل', 'حجم', 0.001, False),
('قطعة', 'قطعة', 'عدد', 1, True),
('علبة', 'علبة', 'عدد', 1, False),
('كرتونة', 'كرتونة', 'عدد', 1, False),
('متر', 'م', 'طول', 1, True),
('سنتيمتر', 'سم', 'طول', 0.01, False);

-- إدراج فئات المواد الأساسية
INSERT INTO فئات_المواد (اسم_الفئة, وصف_الفئة, رمز_الفئة)
VALUES
('مواد خام', 'المواد الخام المستخدمة في الإنتاج', 'RAW'),
('منتجات نهائية', 'المنتجات الجاهزة للبيع', 'FIN'),
('مواد التعبئة والتغليف', 'مواد التعبئة والتغليف', 'PKG'),
('منتجات وسطية', 'منتجات تحت التشغيل', 'WIP'),
('قطع غيار ومستلزمات', 'قطع الغيار والمستلزمات', 'SPA');

-- إدراج المستودعات الأساسية
INSERT INTO المستودعات (اسم_المستودع, موقع_المستودع, نوع_المستودع, مسؤول_المستودع)
VALUES
('مستودع المواد الخام', 'الطابق الأرضي - القسم الشرقي', 'مواد خام', 'أحمد محمد'),
('مستودع المنتجات النهائية', 'الطابق الأول - القسم الغربي', 'منتجات نهائية', 'فاطمة علي'),
('مستودع التعبئة والتغليف', 'الطابق الأرضي - القسم الغربي', 'مواد تعبئة', 'محمد حسن'),
('مستودع تحت التشغيل', 'منطقة الإنتاج', 'تحت التشغيل', 'سارة أحمد');

-- إدراج مراكز التكلفة الأساسية
INSERT INTO مراكز_التكلفة (كود_مركز_التكلفة, اسم_مركز_التكلفة, نوع_مركز_التكلفة, معدل_التحميل_للساعة)
VALUES
('PROD001', 'خط إنتاج المعمول الرئيسي', 'إنتاج', 50.00),
('PROD002', 'قسم التعبئة والتغليف', 'إنتاج', 30.00),
('SERV001', 'قسم مراقبة الجودة', 'خدمات', 40.00),
('ADMIN001', 'الإدارة العامة', 'إداري', 25.00);

-- إدراج إعدادات النظام الأساسية
INSERT INTO إعدادات_النظام (اسم_الإعداد, قيمة_الإعداد, نوع_البيانات, وصف_الإعداد, مجموعة_الإعدادات)
VALUES
('اسم_الشركة', 'مصنع معمول أبو فرح', 'نص', 'اسم الشركة المالكة للنظام', 'عام'),
('عنوان_الشركة', 'القاهرة - مصر', 'نص', 'عنوان الشركة', 'عام'),
('هاتف_الشركة', '02-12345678', 'نص', 'رقم هاتف الشركة', 'عام'),
('طريقة_تقييم_المخزون', 'المتوسط المرجح', 'نص', 'طريقة تقييم المخزون المستخدمة', 'مخزون'),
('العملة_الأساسية', 'جنيه مصري', 'نص', 'العملة الأساسية للنظام', 'محاسبة'),
('معدل_الضريبة_الافتراضي', '14', 'رقم', 'معدل الضريبة الافتراضي بالنسبة المئوية', 'محاسبة'),
('مدة_الصلاحية_الافتراضية', '365', 'رقم', 'مدة الصلاحية الافتراضية بالأيام', 'مخزون');

-- إدراج أرقام المستندات التلقائية
INSERT INTO أرقام_المستندات (نوع_المستند, البادئة, الرقم_الحالي, طول_الرقم)
VALUES
('أمر_شراء', 'PO', 1, 6),
('أمر_بيع', 'SO', 1, 6),
('أمر_إنتاج', 'MO', 1, 6),
('استلام_مواد', 'GR', 1, 6),
('صرف_مواد', 'GI', 1, 6),
('تحويل_مخزون', 'ST', 1, 6),
('تسوية_مخزون', 'IA', 1, 6);
