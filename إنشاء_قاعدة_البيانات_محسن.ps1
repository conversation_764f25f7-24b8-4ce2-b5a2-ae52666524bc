# سكريپت PowerShell محسن لإنشاء قاعدة بيانات Access
# تاريخ الإنشاء: 2025-09-19

Write-Host "بدء إنشاء قاعدة بيانات نظام إدارة تصنيع المعمول..." -ForegroundColor Green

try {
    # مسار قاعدة البيانات الجديدة
    $dbPath = Join-Path (Get-Location) "نظام_إدارة_تصنيع_المعمول.accdb"
    
    # حذف قاعدة البيانات إذا كانت موجودة
    if (Test-Path $dbPath) {
        Remove-Item $dbPath -Force
        Write-Host "تم حذف قاعدة البيانات القديمة" -ForegroundColor Yellow
        Start-Sleep -Seconds 2
    }
    
    # إنشاء كائن DAO Engine
    $dao = New-Object -ComObject DAO.DBEngine.120
    
    # إنشاء قاعدة بيانات جديدة
    $db = $dao.CreateDatabase($dbPath, ";LANGID=0x0401;CP=1256;COUNTRY=0")
    Write-Host "تم إنشاء قاعدة البيانات بنجاح: $dbPath" -ForegroundColor Green
    
    # إنشاء الجداول باستخدام DAO
    Write-Host "بدء إنشاء الجداول..." -ForegroundColor Cyan
    
    # جدول وحدات القياس
    $tabledef = $db.CreateTableDef("وحدات_القياس")
    
    # إضافة الحقول
    $field = $tabledef.CreateField("رقم_الوحدة", 4) # dbLong
    $field.Attributes = 16 # dbAutoIncrField
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("اسم_الوحدة", 10, 50) # dbText
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("اختصار_الوحدة", 10, 10) # dbText
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("نوع_الوحدة", 10, 20) # dbText
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("معامل_التحويل_للوحدة_الأساسية", 7) # dbDouble
    $field.DefaultValue = "1"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("الوحدة_الأساسية", 1) # dbBoolean
    $field.DefaultValue = "False"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("ملاحظات", 12) # dbMemo
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_الإنشاء", 8) # dbDate
    $field.DefaultValue = "Now()"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("نشط", 1) # dbBoolean
    $field.DefaultValue = "True"
    $tabledef.Fields.Append($field)
    
    # إنشاء المفتاح الأساسي
    $index = $tabledef.CreateIndex("PrimaryKey")
    $index.Primary = $true
    $indexfield = $index.CreateField("رقم_الوحدة")
    $index.Fields.Append($indexfield)
    $tabledef.Indexes.Append($index)
    
    # إضافة الجدول إلى قاعدة البيانات
    $db.TableDefs.Append($tabledef)
    Write-Host "تم إنشاء جدول وحدات القياس" -ForegroundColor Green
    
    # جدول المستودعات
    $tabledef = $db.CreateTableDef("المستودعات")
    
    $field = $tabledef.CreateField("رقم_المستودع", 4) # dbLong
    $field.Attributes = 16 # dbAutoIncrField
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("اسم_المستودع", 10, 100) # dbText
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("موقع_المستودع", 10, 200) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("نوع_المستودع", 10, 50) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("مسؤول_المستودع", 10, 100) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("هاتف_المسؤول", 10, 20) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("السعة_القصوى", 7) # dbDouble
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("وحدة_السعة", 10, 20) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("ملاحظات", 12) # dbMemo
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_الإنشاء", 8) # dbDate
    $field.DefaultValue = "Now()"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("نشط", 1) # dbBoolean
    $field.DefaultValue = "True"
    $tabledef.Fields.Append($field)
    
    # إنشاء المفتاح الأساسي
    $index = $tabledef.CreateIndex("PrimaryKey")
    $index.Primary = $true
    $indexfield = $index.CreateField("رقم_المستودع")
    $index.Fields.Append($indexfield)
    $tabledef.Indexes.Append($index)
    
    $db.TableDefs.Append($tabledef)
    Write-Host "تم إنشاء جدول المستودعات" -ForegroundColor Green
    
    # إدراج البيانات الأساسية
    Write-Host "بدء إدراج البيانات الأساسية..." -ForegroundColor Cyan
    
    # إدراج وحدات القياس
    $rs = $db.OpenRecordset("وحدات_القياس")
    
    # كيلوجرام
    $rs.AddNew()
    $rs.Fields("اسم_الوحدة").Value = "كيلوجرام"
    $rs.Fields("اختصار_الوحدة").Value = "كجم"
    $rs.Fields("نوع_الوحدة").Value = "وزن"
    $rs.Fields("معامل_التحويل_للوحدة_الأساسية").Value = 1
    $rs.Fields("الوحدة_الأساسية").Value = $true
    $rs.Update()
    
    # جرام
    $rs.AddNew()
    $rs.Fields("اسم_الوحدة").Value = "جرام"
    $rs.Fields("اختصار_الوحدة").Value = "جم"
    $rs.Fields("نوع_الوحدة").Value = "وزن"
    $rs.Fields("معامل_التحويل_للوحدة_الأساسية").Value = 0.001
    $rs.Fields("الوحدة_الأساسية").Value = $false
    $rs.Update()
    
    # قطعة
    $rs.AddNew()
    $rs.Fields("اسم_الوحدة").Value = "قطعة"
    $rs.Fields("اختصار_الوحدة").Value = "قطعة"
    $rs.Fields("نوع_الوحدة").Value = "عدد"
    $rs.Fields("معامل_التحويل_للوحدة_الأساسية").Value = 1
    $rs.Fields("الوحدة_الأساسية").Value = $true
    $rs.Update()
    
    # علبة
    $rs.AddNew()
    $rs.Fields("اسم_الوحدة").Value = "علبة"
    $rs.Fields("اختصار_الوحدة").Value = "علبة"
    $rs.Fields("نوع_الوحدة").Value = "عدد"
    $rs.Fields("معامل_التحويل_للوحدة_الأساسية").Value = 1
    $rs.Fields("الوحدة_الأساسية").Value = $false
    $rs.Update()
    
    # كرتونة
    $rs.AddNew()
    $rs.Fields("اسم_الوحدة").Value = "كرتونة"
    $rs.Fields("اختصار_الوحدة").Value = "كرتونة"
    $rs.Fields("نوع_الوحدة").Value = "عدد"
    $rs.Fields("معامل_التحويل_للوحدة_الأساسية").Value = 1
    $rs.Fields("الوحدة_الأساسية").Value = $false
    $rs.Update()
    
    $rs.Close()
    Write-Host "تم إدراج وحدات القياس الأساسية" -ForegroundColor Green
    
    # إدراج المستودعات
    $rs = $db.OpenRecordset("المستودعات")
    
    $rs.AddNew()
    $rs.Fields("اسم_المستودع").Value = "مستودع المواد الخام"
    $rs.Fields("موقع_المستودع").Value = "الطابق الأرضي - القسم الشرقي"
    $rs.Fields("نوع_المستودع").Value = "مواد خام"
    $rs.Fields("مسؤول_المستودع").Value = "أحمد محمد"
    $rs.Update()
    
    $rs.AddNew()
    $rs.Fields("اسم_المستودع").Value = "مستودع المنتجات النهائية"
    $rs.Fields("موقع_المستودع").Value = "الطابق الأول - القسم الغربي"
    $rs.Fields("نوع_المستودع").Value = "منتجات نهائية"
    $rs.Fields("مسؤول_المستودع").Value = "فاطمة علي"
    $rs.Update()
    
    $rs.AddNew()
    $rs.Fields("اسم_المستودع").Value = "مستودع التعبئة والتغليف"
    $rs.Fields("موقع_المستودع").Value = "الطابق الأرضي - القسم الغربي"
    $rs.Fields("نوع_المستودع").Value = "مواد تعبئة"
    $rs.Fields("مسؤول_المستودع").Value = "محمد حسن"
    $rs.Update()
    
    $rs.Close()
    Write-Host "تم إدراج المستودعات الأساسية" -ForegroundColor Green
    
    # إغلاق قاعدة البيانات
    $db.Close()
    
    Write-Host "تم إنشاء قاعدة البيانات والجداول الأساسية بنجاح!" -ForegroundColor Green
    Write-Host "مسار الملف: $dbPath" -ForegroundColor Cyan
    Write-Host "سيتم إكمال باقي الجداول في المرحلة التالية..." -ForegroundColor Yellow
    
} catch {
    Write-Host "حدث خطأ أثناء إنشاء قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "تفاصيل الخطأ: $($_.Exception.InnerException)" -ForegroundColor Red
} finally {
    # تنظيف الكائنات
    if ($rs) { try { $rs.Close() } catch {} }
    if ($db) { try { $db.Close() } catch {} }
    if ($dao) {
        try {
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dao) | Out-Null
        } catch {}
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}
