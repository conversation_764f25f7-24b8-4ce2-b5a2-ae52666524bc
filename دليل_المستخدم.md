# دليل المستخدم - نظام إدارة تصنيع المعمول

## نظرة عامة

نظام إدارة تصنيع المعمول هو نظام شامل مصمم خصيصاً لإدارة عمليات تصنيع معمول الجوز والعسل والحلويات التقليدية الأخرى. يوفر النظام إدارة متكاملة للمخزون والتصنيع ومحاسبة التكاليف.

## المتطلبات التقنية

- **نظام التشغيل**: Windows 10 أو أحدث
- **البرامج المطلوبة**: Microsoft Access 2016 أو أحدث
- **المساحة المطلوبة**: 10 MB على الأقل
- **الذاكرة**: 4 GB RAM على الأقل
- **المعالج**: Intel Core i3 أو معادل

## الملفات المتضمنة

### ملفات قاعدة البيانات
- `نظام_إدارة_تصنيع_المعمول.accdb` - قاعدة البيانات الرئيسية
- `نظام_إدارة_تصنيع_المعمول.sql` - سكريبت SQL للنظام

### ملفات الإنشاء والإعداد
- `إنشاء_النظام_النهائي.ps1` - سكريبت إنشاء النظام الكامل
- `إنشاء_قاعدة_البيانات_محسن.ps1` - إنشاء قاعدة البيانات الأساسية
- `إكمال_الجداول.ps1` - إكمال الجداول الأساسية
- `إنشاء_جداول_التصنيع.ps1` - إنشاء جداول التصنيع
- `إدراج_البيانات_الأساسية.ps1` - إدراج البيانات الأولية
- `إنشاء_الوصفة_والمثال.ps1` - إنشاء وصفة المثال التطبيقي

### ملفات التوثيق
- `دليل_المستخدم.md` - هذا الملف
- `README.md` - ملف التعريف بالمشروع

## هيكل قاعدة البيانات

### الجداول الأساسية

#### 1. وحدات_القياس
- **الغرض**: إدارة وحدات القياس المختلفة
- **الحقول الرئيسية**:
  - `رقم_الوحدة` - المفتاح الأساسي
  - `اسم_الوحدة` - اسم الوحدة (كجم، جرام، قطعة، إلخ)
  - `الرمز` - رمز الوحدة المختصر
  - `نوع_الوحدة` - نوع الوحدة (وزن، حجم، عدد)

#### 2. المستودعات
- **الغرض**: إدارة المستودعات والمواقع
- **الحقول الرئيسية**:
  - `رقم_المستودع` - المفتاح الأساسي
  - `اسم_المستودع` - اسم المستودع
  - `الموقع` - موقع المستودع
  - `المسؤول` - مسؤول المستودع

#### 3. فئات_المواد
- **الغرض**: تصنيف المواد والمنتجات
- **الحقول الرئيسية**:
  - `رقم_الفئة` - المفتاح الأساسي
  - `اسم_الفئة` - اسم الفئة
  - `الوصف` - وصف الفئة

#### 4. المواد_والمنتجات
- **الغرض**: قائمة شاملة بجميع المواد والمنتجات
- **الحقول الرئيسية**:
  - `رقم_المادة` - المفتاح الأساسي
  - `اسم_المادة` - اسم المادة
  - `رقم_الفئة` - رقم فئة المادة
  - `وحدة_القياس_الأساسية` - وحدة القياس الأساسية
  - `التكلفة_المعيارية` - التكلفة المعيارية للوحدة
  - `سعر_البيع` - سعر البيع للوحدة
  - `الحد_الأدنى_للمخزون` - الحد الأدنى المطلوب في المخزون

### جداول إدارة المخزون

#### 5. أرصدة_المخزون
- **الغرض**: تتبع أرصدة المواد في المستودعات
- **الحقول الرئيسية**:
  - `رقم_المادة` - رقم المادة
  - `رقم_المستودع` - رقم المستودع
  - `الكمية_المتاحة` - الكمية المتاحة للاستخدام
  - `الكمية_المحجوزة` - الكمية المحجوزة لأوامر الإنتاج
  - `متوسط_التكلفة` - متوسط تكلفة الوحدة
  - `تاريخ_آخر_تحديث` - تاريخ آخر تحديث للرصيد

#### 6. حركات_المخزون
- **الغرض**: تسجيل جميع حركات المخزون (وارد/صادر)
- **الحقول الرئيسية**:
  - `رقم_الحركة` - المفتاح الأساسي
  - `رقم_المادة` - رقم المادة
  - `رقم_المستودع` - رقم المستودع
  - `نوع_الحركة` - نوع الحركة (وارد شراء، صادر إنتاج، إلخ)
  - `الكمية` - كمية الحركة
  - `تكلفة_الوحدة` - تكلفة الوحدة
  - `تاريخ_الحركة` - تاريخ الحركة
  - `رقم_المرجع` - رقم المرجع (رقم أمر الشراء أو الإنتاج)
  - `رقم_الدفعة` - رقم الدفعة
  - `تاريخ_الانتهاء` - تاريخ انتهاء الصلاحية

### جداول التصنيع

#### 7. الوصفات
- **الغرض**: إدارة وصفات الإنتاج
- **الحقول الرئيسية**:
  - `رقم_الوصفة` - المفتاح الأساسي
  - `اسم_الوصفة` - اسم الوصفة
  - `رقم_المنتج_النهائي` - رقم المنتج النهائي
  - `كمية_الإنتاج` - كمية الإنتاج المستهدفة
  - `تكلفة_العمالة` - تكلفة العمالة للوصفة
  - `التكاليف_الإضافية` - التكاليف الإضافية

#### 8. مكونات_الوصفة
- **الغرض**: تفاصيل مكونات كل وصفة
- **الحقول الرئيسية**:
  - `رقم_الوصفة` - رقم الوصفة
  - `رقم_المادة` - رقم المادة المطلوبة
  - `الكمية_المطلوبة` - الكمية المطلوبة من المادة
  - `وحدة_القياس` - وحدة قياس الكمية

#### 9. مراكز_التكلفة
- **الغرض**: إدارة مراكز التكلفة للإنتاج
- **الحقول الرئيسية**:
  - `رقم_مركز_التكلفة` - المفتاح الأساسي
  - `اسم_مركز_التكلفة` - اسم مركز التكلفة
  - `نوع_المركز` - نوع المركز (إنتاج، خدمات، إدارة)

### جداول إدارة الأوامر

#### 10. أوامر_الإنتاج
- **الغرض**: إدارة أوامر الإنتاج
- **الحقول الرئيسية**:
  - `رقم_أمر_الإنتاج` - المفتاح الأساسي
  - `تاريخ_الأمر` - تاريخ إنشاء الأمر
  - `رقم_الوصفة` - رقم الوصفة المستخدمة
  - `الكمية_المطلوبة` - الكمية المطلوب إنتاجها
  - `حالة_الأمر` - حالة الأمر (جديد، قيد التنفيذ، مكتمل)

#### 11. أوامر_الشراء
- **الغرض**: إدارة أوامر الشراء
- **الحقول الرئيسية**:
  - `رقم_أمر_الشراء` - المفتاح الأساسي
  - `تاريخ_الأمر` - تاريخ إنشاء الأمر
  - `رقم_المورد` - رقم المورد
  - `إجمالي_المبلغ` - إجمالي مبلغ الأمر
  - `حالة_الأمر` - حالة الأمر

## المثال التطبيقي: معمول الجوز والعسل

### وصف المنتج
- **الاسم**: معمول الجوز والعسل 600 جرام
- **الكمية المنتجة**: 100 قطعة
- **الوزن**: 600 جرام للقطعة الواحدة

### المكونات المطلوبة
1. **دقيق قمح**: 30 كجم (8.50 جنيه/كجم) = 255 جنيه
2. **جوز مقشر**: 8 كجم (45.00 جنيه/كجم) = 360 جنيه
3. **عسل نحل**: 5 كجم (80.00 جنيه/كجم) = 400 جنيه
4. **زبدة طبيعية**: 6 كجم (25.00 جنيه/كجم) = 150 جنيه
5. **سكر ناعم**: 4 كجم (12.00 جنيه/كجم) = 48 جنيه
6. **بيكنج باودر**: 200 جرام (0.015 جنيه/جرام) = 3 جنيه
7. **ملح طعام**: 50 جرام (0.003 جنيه/جرام) = 0.15 جنيه
8. **علب كرتونية**: 100 قطعة (2.50 جنيه/قطعة) = 250 جنيه

### تحليل التكلفة
- **تكلفة المواد الخام**: 1,466.15 جنيه
- **تكلفة العمالة**: 800 جنيه
- **التكاليف الإضافية**: 200 جنيه
- **إجمالي التكلفة**: 2,466.15 جنيه
- **تكلفة القطعة الواحدة**: 24.66 جنيه

### تحليل الربحية
- **سعر البيع المقترح**: 85.00 جنيه للقطعة
- **هامش الربح**: 60.34 جنيه للقطعة
- **نسبة الربح**: 244.88%
- **إجمالي الإيرادات (100 قطعة)**: 8,500 جنيه
- **صافي الربح**: 6,033.85 جنيه

## كيفية الاستخدام

### 1. فتح النظام
1. انتقل إلى مجلد النظام
2. انقر نقراً مزدوجاً على ملف `نظام_إدارة_تصنيع_المعمول.accdb`
3. إذا ظهرت رسالة أمان، اختر "تمكين المحتوى"

### 2. استعراض البيانات
- **الجداول**: تحتوي على جميع البيانات الأساسية
- **الاستعلامات**: تقارير محددة مسبقاً
- **النماذج**: واجهات لإدخال وتعديل البيانات
- **التقارير**: تقارير مطبوعة

### 3. إدخال بيانات جديدة
1. افتح الجدول المطلوب
2. انقر على "سجل جديد"
3. أدخل البيانات المطلوبة
4. احفظ السجل

### 4. تشغيل التقارير
1. انتقل إلى قسم "التقارير"
2. انقر نقراً مزدوجاً على التقرير المطلوب
3. اختر المعايير إذا طُلب منك ذلك
4. اطبع أو احفظ التقرير

## الميزات المتقدمة

### 1. نظام محاسبة التكاليف
- حساب التكلفة بطريقة المتوسط المرجح
- تتبع تكاليف المواد والعمالة والتكاليف الإضافية
- تحليل الربحية لكل منتج

### 2. إدارة المخزون
- تتبع الأرصدة في الوقت الفعلي
- تنبيهات عند الوصول للحد الأدنى
- تتبع تواريخ الانتهاء والدفعات

### 3. إدارة الوصفات
- وصفات مرنة قابلة للتعديل
- حساب المكونات تلقائياً حسب الكمية المطلوبة
- تحليل تكلفة كل وصفة

### 4. التقارير الشاملة
- تقارير المخزون والحركات
- تقارير التكاليف والربحية
- تقارير الإنتاج والمبيعات

## الصيانة والنسخ الاحتياطي

### النسخ الاحتياطي
1. انسخ ملف `نظام_إدارة_تصنيع_المعمول.accdb` إلى مكان آمن
2. قم بعمل نسخة احتياطية يومياً
3. احتفظ بنسخ متعددة في أماكن مختلفة

### الصيانة الدورية
1. ضغط وإصلاح قاعدة البيانات شهرياً
2. تنظيف البيانات القديمة غير المطلوبة
3. تحديث كلمات المرور بانتظام

## الدعم الفني

### المشاكل الشائعة
1. **قاعدة البيانات مؤمنة**: أغلق جميع نوافذ Access وأعد المحاولة
2. **بطء في الأداء**: قم بضغط قاعدة البيانات
3. **خطأ في البيانات**: تحقق من صحة المدخلات

### التواصل للدعم
- للدعم الفني أو التطوير، يرجى التواصل مع فريق التطوير
- يمكن تخصيص النظام حسب احتياجاتك الخاصة

## حقوق الطبع والنشر

هذا النظام مطور خصيصاً لإدارة تصنيع المعمول والحلويات التقليدية. جميع الحقوق محفوظة.

---

**تاريخ آخر تحديث**: سبتمبر 2025  
**الإصدار**: 1.0  
**اللغة**: العربية
