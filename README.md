# نظام إدارة تصنيع المعمول 🧁

نظام شامل لإدارة تصنيع معمول الجوز والعسل والحلويات التقليدية باستخدام Microsoft Access

## 📋 نظرة عامة

نظام إدارة تصنيع المعمول هو حل متكامل مصمم خصيصاً للشركات الصغيرة والمتوسطة العاملة في مجال تصنيع الحلويات التقليدية، وخاصة المعمول. يوفر النظام إدارة شاملة للمخزون والتصنيع ومحاسبة التكاليف مع واجهة عربية كاملة.

## ✨ الميزات الرئيسية

### 🏭 إدارة التصنيع
- ✅ إدارة الوصفات والمكونات
- ✅ تتبع أوامر الإنتاج
- ✅ حساب التكاليف التلقائي
- ✅ تحليل الربحية

### 📦 إدارة المخزون
- ✅ تتبع الأرصدة في الوقت الفعلي
- ✅ إدارة متعددة المستودعات
- ✅ تتبع الدفعات وتواريخ الانتهاء
- ✅ تنبيهات الحد الأدنى للمخزون

### 💰 محاسبة التكاليف
- ✅ نظام التكلفة بالمتوسط المرجح
- ✅ تتبع تكاليف المواد والعمالة
- ✅ حساب هوامش الربح
- ✅ تحليل الربحية لكل منتج

### 📊 التقارير والتحليلات
- ✅ تقارير المخزون والحركات
- ✅ تقارير التكاليف والربحية
- ✅ تقارير الإنتاج والمبيعات
- ✅ تحليلات مالية شاملة

## 🛠️ المتطلبات التقنية

- **نظام التشغيل**: Windows 10 أو أحدث
- **البرامج**: Microsoft Access 2016 أو أحدث
- **المساحة**: 10 MB على الأقل
- **الذاكرة**: 4 GB RAM
- **المعالج**: Intel Core i3 أو معادل

## 📁 محتويات المشروع

```
نظام_إدارة_تصنيع_المعمول/
├── نظام_إدارة_تصنيع_المعمول.accdb    # قاعدة البيانات الرئيسية
├── نظام_إدارة_تصنيع_المعمول.sql      # سكريبت SQL
├── إنشاء_النظام_النهائي.ps1           # سكريبت الإنشاء الكامل
├── إنشاء_قاعدة_البيانات_محسن.ps1      # إنشاء قاعدة البيانات
├── إكمال_الجداول.ps1                  # إكمال الجداول
├── إنشاء_جداول_التصنيع.ps1            # جداول التصنيع
├── إدراج_البيانات_الأساسية.ps1        # البيانات الأولية
├── إنشاء_الوصفة_والمثال.ps1           # المثال التطبيقي
├── دليل_المستخدم.md                   # دليل المستخدم الشامل
└── README.md                          # هذا الملف
```

## 🚀 التثبيت والتشغيل

### الطريقة السريعة
1. قم بتحميل جميع الملفات
2. شغل سكريبت `إنشاء_النظام_النهائي.ps1` في PowerShell:
   ```powershell
   powershell -ExecutionPolicy Bypass -File "إنشاء_النظام_النهائي.ps1"
   ```
3. افتح ملف `نظام_إدارة_تصنيع_المعمول.accdb`

### الطريقة اليدوية
1. افتح ملف `نظام_إدارة_تصنيع_المعمول.accdb` مباشرة
2. إذا ظهرت رسالة أمان، اختر "تمكين المحتوى"
3. ابدأ في استخدام النظام

## 🧁 المثال التطبيقي: معمول الجوز والعسل

### 📊 تفاصيل المنتج
- **الاسم**: معمول الجوز والعسل 600 جرام
- **الكمية**: 100 قطعة
- **التكلفة**: 24.66 جنيه/قطعة
- **سعر البيع**: 85.00 جنيه/قطعة
- **هامش الربح**: 244.88%

### 🥜 المكونات
| المادة | الكمية | التكلفة |
|--------|---------|---------|
| دقيق قمح | 30 كجم | 255 جنيه |
| جوز مقشر | 8 كجم | 360 جنيه |
| عسل نحل | 5 كجم | 400 جنيه |
| زبدة طبيعية | 6 كجم | 150 جنيه |
| سكر ناعم | 4 كجم | 48 جنيه |
| بيكنج باودر | 200 جرام | 3 جنيه |
| ملح طعام | 50 جرام | 0.15 جنيه |
| علب كرتونية | 100 قطعة | 250 جنيه |

### 💰 تحليل التكلفة
- **تكلفة المواد**: 1,466.15 جنيه
- **تكلفة العمالة**: 800 جنيه
- **تكاليف إضافية**: 200 جنيه
- **إجمالي التكلفة**: 2,466.15 جنيه
- **إجمالي الإيرادات**: 8,500 جنيه
- **صافي الربح**: 6,033.85 جنيه

## 🗂️ هيكل قاعدة البيانات

### الجداول الأساسية
- `وحدات_القياس` - إدارة وحدات القياس
- `المستودعات` - إدارة المستودعات
- `فئات_المواد` - تصنيف المواد
- `المواد_والمنتجات` - قائمة المواد والمنتجات
- `الموردين` - بيانات الموردين
- `العملاء` - بيانات العملاء

### جداول المخزون
- `أرصدة_المخزون` - أرصدة المواد
- `حركات_المخزون` - تتبع الحركات

### جداول التصنيع
- `الوصفات` - وصفات الإنتاج
- `مكونات_الوصفة` - مكونات الوصفات
- `مراكز_التكلفة` - مراكز التكلفة
- `أوامر_الإنتاج` - أوامر الإنتاج
- `أوامر_الشراء` - أوامر الشراء

## 📖 كيفية الاستخدام

### 1. البدء السريع
1. افتح ملف قاعدة البيانات
2. استعرض الجداول لفهم البيانات الموجودة
3. استخدم النماذج لإدخال بيانات جديدة
4. اطلع على التقارير للتحليل

### 2. إدارة المخزون
- أضف مواد جديدة في جدول `المواد_والمنتجات`
- سجل حركات الشراء في `حركات_المخزون`
- راقب الأرصدة في `أرصدة_المخزون`

### 3. إدارة الإنتاج
- أنشئ وصفات جديدة في `الوصفات`
- حدد المكونات في `مكونات_الوصفة`
- أنشئ أوامر إنتاج جديدة

### 4. التقارير والتحليل
- استخدم التقارير المحددة مسبقاً
- أنشئ استعلامات مخصصة
- حلل البيانات لاتخاذ قرارات أفضل

## 🔧 التخصيص والتطوير

النظام قابل للتخصيص بالكامل:
- إضافة جداول جديدة
- تعديل النماذج والتقارير
- إنشاء استعلامات مخصصة
- تطوير ميزات إضافية

## 🛡️ الأمان والنسخ الاحتياطي

### النسخ الاحتياطي
- انسخ ملف `.accdb` بانتظام
- احتفظ بنسخ في أماكن متعددة
- استخدم خدمات التخزين السحابي

### الأمان
- استخدم كلمات مرور قوية
- قم بتحديث النظام بانتظام
- راقب الوصول للبيانات

## 🐛 استكشاف الأخطاء

### المشاكل الشائعة
1. **قاعدة البيانات مؤمنة**: أغلق Access وأعد المحاولة
2. **بطء الأداء**: قم بضغط قاعدة البيانات
3. **أخطاء البيانات**: تحقق من صحة المدخلات

### الحلول
- راجع دليل المستخدم للتفاصيل
- تحقق من المتطلبات التقنية
- تواصل مع فريق الدعم

## 📞 الدعم والتواصل

للدعم الفني أو التطوير:
- يمكن تخصيص النظام حسب احتياجاتك
- متوفر دعم فني شامل
- إمكانية التدريب على النظام

## 📄 الترخيص

هذا النظام مطور خصيصاً لإدارة تصنيع المعمول والحلويات التقليدية.
جميع الحقوق محفوظة.

## 🏷️ الكلمات المفتاحية

`معمول` `تصنيع` `إدارة مخزون` `محاسبة تكاليف` `Microsoft Access` `حلويات` `إنتاج` `عربي`

---

**الإصدار**: 1.0  
**تاريخ الإنشاء**: سبتمبر 2025  
**اللغة**: العربية  
**المطور**: فريق تطوير أنظمة التصنيع

---

### 🌟 شكراً لاستخدام نظام إدارة تصنيع المعمول!

نتمنى أن يساعدك هذا النظام في إدارة أعمالك بكفاءة أكبر وتحقيق أرباح أفضل. 

للمزيد من المعلومات، راجع `دليل_المستخدم.md`
