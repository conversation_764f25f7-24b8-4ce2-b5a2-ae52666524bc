# سكريبت إضافة الاستعلامات باستخدام DAO
# Script to Add Queries Using DAO

Write-Host "===============================================" -ForegroundColor Magenta
Write-Host "        إضافة الاستعلامات باستخدام DAO        " -ForegroundColor Magenta
Write-Host "===============================================" -ForegroundColor Magenta
Write-Host ""

# إغلاق أي عمليات Access مفتوحة
Write-Host "إغلاق أي عمليات Access مفتوحة..." -ForegroundColor Yellow
Get-Process -Name "MSACCESS" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 3

$dbPath = "نظام_إدارة_تصنيع_المعمول_جديد.accdb"

if (-not (Test-Path $dbPath)) {
    Write-Host "خطأ: قاعدة البيانات غير موجودة." -ForegroundColor Red
    exit
}

try {
    Write-Host "فتح قاعدة البيانات لإضافة الاستعلامات..." -ForegroundColor Green
    
    # استخدام DAO لإضافة الاستعلامات
    $dao = New-Object -ComObject DAO.DBEngine.120
    $db = $dao.OpenDatabase($dbPath)
    
    Write-Host "إضافة الاستعلامات..." -ForegroundColor Cyan
    
    # 1. استعلام أرصدة المخزون
    $querySQL = @"
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    ف.اسم_الفئة,
    مس.اسم_المستودع,
    أ.رقم_الدفعة,
    أ.الكمية_المتاحة,
    و.الرمز AS وحدة_القياس,
    أ.التكلفة_الوحدة,
    (أ.الكمية_المتاحة * أ.التكلفة_الوحدة) AS إجمالي_القيمة
FROM ((((أرصدة_المخزون أ 
INNER JOIN المواد_والمنتجات م ON أ.رقم_المادة = م.رقم_المادة)
INNER JOIN فئات_المواد ف ON م.رقم_الفئة = ف.رقم_الفئة)
INNER JOIN المستودعات مس ON أ.رقم_المستودع = مس.رقم_المستودع)
INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة)
WHERE أ.الكمية_المتاحة > 0
ORDER BY م.اسم_المادة, مس.اسم_المستودع;
"@
    
    try {
        $qdf = $db.CreateQueryDef("استعلام_أرصدة_المخزون", $querySQL)
        Write-Host "✓ تم إنشاء استعلام أرصدة المخزون" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام أرصدة المخزون موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 2. استعلام المواد تحت الحد الأدنى
    $querySQL = @"
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    ف.اسم_الفئة,
    Sum(أ.الكمية_المتاحة) AS إجمالي_الكمية,
    م.الحد_الأدنى_للمخزون,
    و.الرمز AS وحدة_القياس
FROM (((أرصدة_المخزون أ 
INNER JOIN المواد_والمنتجات م ON أ.رقم_المادة = م.رقم_المادة)
INNER JOIN فئات_المواد ف ON م.رقم_الفئة = ف.رقم_الفئة)
INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة)
GROUP BY م.كود_المادة, م.اسم_المادة, ف.اسم_الفئة, 
         م.الحد_الأدنى_للمخزون, و.الرمز
HAVING Sum(أ.الكمية_المتاحة) <= م.الحد_الأدنى_للمخزون
ORDER BY م.اسم_المادة;
"@
    
    try {
        $qdf = $db.CreateQueryDef("استعلام_المواد_تحت_الحد_الأدنى", $querySQL)
        Write-Host "✓ تم إنشاء استعلام المواد تحت الحد الأدنى" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام المواد تحت الحد الأدنى موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 3. استعلام تفاصيل الوصفة مع التكلفة
    $querySQL = @"
SELECT 
    و.اسم_الوصفة,
    مك.رقم_المكون,
    م.كود_المادة,
    م.اسم_المادة,
    مك.الكمية_المطلوبة,
    وحدة.الرمز AS وحدة_القياس,
    مك.التكلفة_المعيارية,
    (مك.الكمية_المطلوبة * مك.التكلفة_المعيارية) AS إجمالي_التكلفة
FROM (((مكونات_الوصفة مك 
INNER JOIN الوصفات و ON مك.رقم_الوصفة = و.رقم_الوصفة)
INNER JOIN المواد_والمنتجات م ON مك.رقم_المادة = م.رقم_المادة)
INNER JOIN وحدات_القياس وحدة ON مك.وحدة_القياس = وحدة.رقم_الوحدة)
ORDER BY و.اسم_الوصفة, مك.رقم_المكون;
"@
    
    try {
        $qdf = $db.CreateQueryDef("استعلام_تفاصيل_الوصفة", $querySQL)
        Write-Host "✓ تم إنشاء استعلام تفاصيل الوصفة" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام تفاصيل الوصفة موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 4. استعلام إجمالي تكلفة الوصفة
    $querySQL = @"
SELECT 
    و.اسم_الوصفة,
    و.كمية_الإنتاج,
    Sum(مك.الكمية_المطلوبة * مك.التكلفة_المعيارية) AS إجمالي_تكلفة_الوصفة,
    (Sum(مك.الكمية_المطلوبة * مك.التكلفة_المعيارية) / و.كمية_الإنتاج) AS تكلفة_الوحدة
FROM (مكونات_الوصفة مك 
INNER JOIN الوصفات و ON مك.رقم_الوصفة = و.رقم_الوصفة)
GROUP BY و.اسم_الوصفة, و.كمية_الإنتاج
ORDER BY و.اسم_الوصفة;
"@
    
    try {
        $qdf = $db.CreateQueryDef("استعلام_تكلفة_الوصفة", $querySQL)
        Write-Host "✓ تم إنشاء استعلام تكلفة الوصفة" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام تكلفة الوصفة موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 5. استعلام قائمة المواد والأسعار
    $querySQL = @"
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    ف.اسم_الفئة,
    م.التكلفة_المعيارية,
    م.سعر_البيع,
    و.الرمز AS وحدة_القياس,
    م.الحد_الأدنى_للمخزون,
    م.نوع_المادة
FROM ((المواد_والمنتجات م 
INNER JOIN فئات_المواد ف ON م.رقم_الفئة = ف.رقم_الفئة)
INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة)
ORDER BY ف.اسم_الفئة, م.اسم_المادة;
"@
    
    try {
        $qdf = $db.CreateQueryDef("استعلام_قائمة_المواد", $querySQL)
        Write-Host "✓ تم إنشاء استعلام قائمة المواد" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام قائمة المواد موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 6. استعلام تحليل الربحية
    $querySQL = @"
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    م.التكلفة_المعيارية,
    م.سعر_البيع,
    (م.سعر_البيع - م.التكلفة_المعيارية) AS هامش_الربح,
    IIf(م.التكلفة_المعيارية > 0, ((م.سعر_البيع - م.التكلفة_المعيارية) / م.التكلفة_المعيارية) * 100, 0) AS نسبة_الربح
FROM المواد_والمنتجات م
WHERE م.سعر_البيع > 0
ORDER BY نسبة_الربح DESC;
"@
    
    try {
        $qdf = $db.CreateQueryDef("استعلام_تحليل_الربحية", $querySQL)
        Write-Host "✓ تم إنشاء استعلام تحليل الربحية" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام تحليل الربحية موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 7. استعلام إجمالي قيمة المخزون
    $querySQL = @"
SELECT 
    ف.اسم_الفئة,
    مس.اسم_المستودع,
    Sum(أ.الكمية_المتاحة * أ.التكلفة_الوحدة) AS إجمالي_قيمة_المخزون,
    Count(*) AS عدد_المواد
FROM (((أرصدة_المخزون أ 
INNER JOIN المواد_والمنتجات م ON أ.رقم_المادة = م.رقم_المادة)
INNER JOIN فئات_المواد ف ON م.رقم_الفئة = ف.رقم_الفئة)
INNER JOIN المستودعات مس ON أ.رقم_المستودع = مس.رقم_المستودع)
WHERE أ.الكمية_المتاحة > 0
GROUP BY ف.اسم_الفئة, مس.اسم_المستودع
ORDER BY ف.اسم_الفئة, مس.اسم_المستودع;
"@
    
    try {
        $qdf = $db.CreateQueryDef("استعلام_قيمة_المخزون", $querySQL)
        Write-Host "✓ تم إنشاء استعلام قيمة المخزون" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام قيمة المخزون موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # إغلاق قاعدة البيانات
    $db.Close()
    
    Write-Host ""
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host "        تم إنجاز الاستعلامات بنجاح كامل!      " -ForegroundColor Green
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "📊 ملخص الاستعلامات المنشأة:" -ForegroundColor Cyan
    Write-Host "✓ استعلام أرصدة المخزون" -ForegroundColor Green
    Write-Host "✓ استعلام المواد تحت الحد الأدنى" -ForegroundColor Green
    Write-Host "✓ استعلام تفاصيل الوصفة" -ForegroundColor Green
    Write-Host "✓ استعلام تكلفة الوصفة" -ForegroundColor Green
    Write-Host "✓ استعلام قائمة المواد" -ForegroundColor Green
    Write-Host "✓ استعلام تحليل الربحية" -ForegroundColor Green
    Write-Host "✓ استعلام قيمة المخزون" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "🎯 وظائف الاستعلامات:" -ForegroundColor Cyan
    Write-Host "• عرض أرصدة المخزون الحالية مع القيم" -ForegroundColor White
    Write-Host "• تحديد المواد التي تحتاج إعادة طلب" -ForegroundColor White
    Write-Host "• عرض تفاصيل مكونات الوصفات" -ForegroundColor White
    Write-Host "• حساب تكلفة الإنتاج للوصفات" -ForegroundColor White
    Write-Host "• عرض قائمة المواد مع الأسعار" -ForegroundColor White
    Write-Host "• تحليل ربحية المنتجات" -ForegroundColor White
    Write-Host "• تقييم إجمالي قيمة المخزون" -ForegroundColor White
    Write-Host ""
    
    # فتح قاعدة البيانات لعرض النتائج
    Write-Host "فتح قاعدة البيانات لعرض الاستعلامات..." -ForegroundColor Green
    Start-Process $dbPath
    Write-Host "✓ تم فتح قاعدة البيانات في Microsoft Access" -ForegroundColor Green
    
} catch {
    Write-Host "خطأ في إضافة الاستعلامات: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # تنظيف الكائنات
    if ($db) { $db.Close() }
    if ($dao) { [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dao) | Out-Null }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}

Write-Host ""
Write-Host "===============================================" -ForegroundColor Magenta
Write-Host "    الاستعلامات جاهزة للاستخدام الفوري!      " -ForegroundColor Magenta
Write-Host "===============================================" -ForegroundColor Magenta
