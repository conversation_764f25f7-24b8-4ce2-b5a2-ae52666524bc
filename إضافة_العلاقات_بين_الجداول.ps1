# سكريبت إضافة العلاقات بين الجداول
# Script to Add Relationships Between Tables

Write-Host "===============================================" -ForegroundColor Magenta
Write-Host "        إضافة العلاقات بين الجداول          " -ForegroundColor Magenta
Write-Host "===============================================" -ForegroundColor Magenta
Write-Host ""

# إغلاق أي عمليات Access مفتوحة
Write-Host "إغلاق أي عمليات Access مفتوحة..." -ForegroundColor Yellow
Get-Process -Name "MSACCESS" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 3

$dbPath = "نظام_إدارة_تصنيع_المعمول_جديد.accdb"

if (-not (Test-Path $dbPath)) {
    Write-Host "خطأ: قاعدة البيانات غير موجودة." -ForegroundColor Red
    exit
}

try {
    Write-Host "فتح قاعدة البيانات لإضافة العلاقات..." -ForegroundColor Green
    
    # استخدام DAO لإضافة العلاقات
    $dao = New-Object -ComObject DAO.DBEngine.120
    $db = $dao.OpenDatabase($dbPath)
    
    Write-Host "إضافة العلاقات بين الجداول..." -ForegroundColor Cyan
    
    # 1. علاقة بين المواد والمنتجات وفئات المواد
    try {
        $rel = $db.CreateRelation("علاقة_المواد_الفئات")
        $rel.Table = "فئات_المواد"
        $rel.ForeignTable = "المواد_والمنتجات"
        $rel.Attributes = 256  # dbRelationUpdateCascade
        
        $field = $rel.CreateField("رقم_الفئة")
        $field.ForeignName = "رقم_الفئة"
        $rel.Fields.Append($field)
        
        $db.Relations.Append($rel)
        Write-Host "✓ تم إنشاء علاقة المواد والفئات" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: علاقة المواد والفئات موجودة مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 2. علاقة بين المواد والمنتجات ووحدات القياس
    try {
        $rel = $db.CreateRelation("علاقة_المواد_الوحدات")
        $rel.Table = "وحدات_القياس"
        $rel.ForeignTable = "المواد_والمنتجات"
        $rel.Attributes = 256  # dbRelationUpdateCascade
        
        $field = $rel.CreateField("رقم_الوحدة")
        $field.ForeignName = "وحدة_القياس_الأساسية"
        $rel.Fields.Append($field)
        
        $db.Relations.Append($rel)
        Write-Host "✓ تم إنشاء علاقة المواد ووحدات القياس" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: علاقة المواد ووحدات القياس موجودة مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 3. علاقة بين أرصدة المخزون والمواد والمنتجات
    try {
        $rel = $db.CreateRelation("علاقة_الأرصدة_المواد")
        $rel.Table = "المواد_والمنتجات"
        $rel.ForeignTable = "أرصدة_المخزون"
        $rel.Attributes = 256  # dbRelationUpdateCascade
        
        $field = $rel.CreateField("رقم_المادة")
        $field.ForeignName = "رقم_المادة"
        $rel.Fields.Append($field)
        
        $db.Relations.Append($rel)
        Write-Host "✓ تم إنشاء علاقة الأرصدة والمواد" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: علاقة الأرصدة والمواد موجودة مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 4. علاقة بين أرصدة المخزون والمستودعات
    try {
        $rel = $db.CreateRelation("علاقة_الأرصدة_المستودعات")
        $rel.Table = "المستودعات"
        $rel.ForeignTable = "أرصدة_المخزون"
        $rel.Attributes = 256  # dbRelationUpdateCascade
        
        $field = $rel.CreateField("رقم_المستودع")
        $field.ForeignName = "رقم_المستودع"
        $rel.Fields.Append($field)
        
        $db.Relations.Append($rel)
        Write-Host "✓ تم إنشاء علاقة الأرصدة والمستودعات" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: علاقة الأرصدة والمستودعات موجودة مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 5. علاقة بين حركات المخزون والمواد والمنتجات
    try {
        $rel = $db.CreateRelation("علاقة_الحركات_المواد")
        $rel.Table = "المواد_والمنتجات"
        $rel.ForeignTable = "حركات_المخزون"
        $rel.Attributes = 256  # dbRelationUpdateCascade
        
        $field = $rel.CreateField("رقم_المادة")
        $field.ForeignName = "رقم_المادة"
        $rel.Fields.Append($field)
        
        $db.Relations.Append($rel)
        Write-Host "✓ تم إنشاء علاقة الحركات والمواد" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: علاقة الحركات والمواد موجودة مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 6. علاقة بين حركات المخزون والمستودعات
    try {
        $rel = $db.CreateRelation("علاقة_الحركات_المستودعات")
        $rel.Table = "المستودعات"
        $rel.ForeignTable = "حركات_المخزون"
        $rel.Attributes = 256  # dbRelationUpdateCascade
        
        $field = $rel.CreateField("رقم_المستودع")
        $field.ForeignName = "رقم_المستودع"
        $rel.Fields.Append($field)
        
        $db.Relations.Append($rel)
        Write-Host "✓ تم إنشاء علاقة الحركات والمستودعات" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: علاقة الحركات والمستودعات موجودة مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 7. علاقة بين الوصفات والمواد والمنتجات
    try {
        $rel = $db.CreateRelation("علاقة_الوصفات_المنتجات")
        $rel.Table = "المواد_والمنتجات"
        $rel.ForeignTable = "الوصفات"
        $rel.Attributes = 256  # dbRelationUpdateCascade
        
        $field = $rel.CreateField("رقم_المادة")
        $field.ForeignName = "رقم_المنتج"
        $rel.Fields.Append($field)
        
        $db.Relations.Append($rel)
        Write-Host "✓ تم إنشاء علاقة الوصفات والمنتجات" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: علاقة الوصفات والمنتجات موجودة مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 8. علاقة بين مكونات الوصفة والوصفات
    try {
        $rel = $db.CreateRelation("علاقة_المكونات_الوصفات")
        $rel.Table = "الوصفات"
        $rel.ForeignTable = "مكونات_الوصفة"
        $rel.Attributes = 768  # dbRelationUpdateCascade + dbRelationDeleteCascade
        
        $field = $rel.CreateField("رقم_الوصفة")
        $field.ForeignName = "رقم_الوصفة"
        $rel.Fields.Append($field)
        
        $db.Relations.Append($rel)
        Write-Host "✓ تم إنشاء علاقة المكونات والوصفات" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: علاقة المكونات والوصفات موجودة مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 9. علاقة بين مكونات الوصفة والمواد والمنتجات
    try {
        $rel = $db.CreateRelation("علاقة_المكونات_المواد")
        $rel.Table = "المواد_والمنتجات"
        $rel.ForeignTable = "مكونات_الوصفة"
        $rel.Attributes = 256  # dbRelationUpdateCascade
        
        $field = $rel.CreateField("رقم_المادة")
        $field.ForeignName = "رقم_المادة"
        $rel.Fields.Append($field)
        
        $db.Relations.Append($rel)
        Write-Host "✓ تم إنشاء علاقة المكونات والمواد" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: علاقة المكونات والمواد موجودة مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 10. علاقة بين مكونات الوصفة ووحدات القياس
    try {
        $rel = $db.CreateRelation("علاقة_المكونات_الوحدات")
        $rel.Table = "وحدات_القياس"
        $rel.ForeignTable = "مكونات_الوصفة"
        $rel.Attributes = 256  # dbRelationUpdateCascade
        
        $field = $rel.CreateField("رقم_الوحدة")
        $field.ForeignName = "وحدة_القياس"
        $rel.Fields.Append($field)
        
        $db.Relations.Append($rel)
        Write-Host "✓ تم إنشاء علاقة المكونات ووحدات القياس" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: علاقة المكونات ووحدات القياس موجودة مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # إغلاق قاعدة البيانات
    $db.Close()
    
    Write-Host ""
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host "        تم إنجاز العلاقات بنجاح كامل!        " -ForegroundColor Green
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "📊 ملخص العلاقات المنشأة:" -ForegroundColor Cyan
    Write-Host "✓ علاقة المواد والفئات" -ForegroundColor Green
    Write-Host "✓ علاقة المواد ووحدات القياس" -ForegroundColor Green
    Write-Host "✓ علاقة الأرصدة والمواد" -ForegroundColor Green
    Write-Host "✓ علاقة الأرصدة والمستودعات" -ForegroundColor Green
    Write-Host "✓ علاقة الحركات والمواد" -ForegroundColor Green
    Write-Host "✓ علاقة الحركات والمستودعات" -ForegroundColor Green
    Write-Host "✓ علاقة الوصفات والمنتجات" -ForegroundColor Green
    Write-Host "✓ علاقة المكونات والوصفات" -ForegroundColor Green
    Write-Host "✓ علاقة المكونات والمواد" -ForegroundColor Green
    Write-Host "✓ علاقة المكونات ووحدات القياس" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "🎯 مميزات العلاقات:" -ForegroundColor Cyan
    Write-Host "• تحديث متتالي (Cascade Update)" -ForegroundColor White
    Write-Host "• حذف متتالي للمكونات عند حذف الوصفة" -ForegroundColor White
    Write-Host "• ضمان سلامة البيانات (Referential Integrity)" -ForegroundColor White
    Write-Host "• منع إدخال بيانات غير صحيحة" -ForegroundColor White
    Write-Host ""
    
} catch {
    Write-Host "خطأ في إضافة العلاقات: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # تنظيف الكائنات
    if ($db) { $db.Close() }
    if ($dao) { [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dao) | Out-Null }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}

Write-Host "===============================================" -ForegroundColor Magenta
Write-Host "    العلاقات جاهزة - يمكن الآن إضافة الباقي!   " -ForegroundColor Magenta
Write-Host "===============================================" -ForegroundColor Magenta
