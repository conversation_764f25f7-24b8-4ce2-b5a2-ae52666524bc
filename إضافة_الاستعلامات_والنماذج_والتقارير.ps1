# سكريبت إضافة الاستعلامات والنماذج والتقارير إلى قاعدة البيانات
# Script to Add Queries, Forms, and Reports to Database

Write-Host "===============================================" -ForegroundColor Magenta
Write-Host "    إضافة الاستعلامات والنماذج والتقارير     " -ForegroundColor Magenta
Write-Host "===============================================" -ForegroundColor Magenta
Write-Host ""

$dbPath = "نظام_إدارة_تصنيع_المعمول_جديد.accdb"

if (-not (Test-Path $dbPath)) {
    Write-Host "خطأ: قاعدة البيانات غير موجودة. يرجى تشغيل سكريبت إنشاء النظام أولاً." -ForegroundColor Red
    exit
}

try {
    # فتح Access
    Write-Host "فتح Microsoft Access..." -ForegroundColor Green
    $access = New-Object -ComObject Access.Application
    $access.OpenCurrentDatabase($dbPath)
    $access.Visible = $false
    
    Write-Host "إنشاء الاستعلامات..." -ForegroundColor Cyan
    
    # 1. استعلام أرصدة المخزون
    $querySQL = @"
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    ف.اسم_الفئة,
    مس.اسم_المستودع,
    أ.رقم_الدفعة,
    أ.الكمية_المتاحة,
    و.الرمز AS وحدة_القياس,
    أ.التكلفة_الوحدة,
    (أ.الكمية_المتاحة * أ.التكلفة_الوحدة) AS إجمالي_القيمة
FROM ((((أرصدة_المخزون أ 
INNER JOIN المواد_والمنتجات م ON أ.رقم_المادة = م.رقم_المادة)
INNER JOIN فئات_المواد ف ON م.رقم_الفئة = ف.رقم_الفئة)
INNER JOIN المستودعات مس ON أ.رقم_المستودع = مس.رقم_المستودع)
INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة)
WHERE أ.الكمية_المتاحة > 0
ORDER BY م.اسم_المادة, مس.اسم_المستودع;
"@
    
    try {
        $qdf = $access.CurrentDb.CreateQueryDef("استعلام_أرصدة_المخزون", $querySQL)
        Write-Host "✓ تم إنشاء استعلام أرصدة المخزون" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام أرصدة المخزون موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 2. استعلام المواد تحت الحد الأدنى
    $querySQL = @"
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    ف.اسم_الفئة,
    Sum(أ.الكمية_المتاحة) AS إجمالي_الكمية,
    م.الحد_الأدنى_للمخزون,
    و.الرمز AS وحدة_القياس
FROM (((أرصدة_المخزون أ 
INNER JOIN المواد_والمنتجات م ON أ.رقم_المادة = م.رقم_المادة)
INNER JOIN فئات_المواد ف ON م.رقم_الفئة = ف.رقم_الفئة)
INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة)
GROUP BY م.كود_المادة, م.اسم_المادة, ف.اسم_الفئة, 
         م.الحد_الأدنى_للمخزون, و.الرمز
HAVING Sum(أ.الكمية_المتاحة) <= م.الحد_الأدنى_للمخزون
ORDER BY م.اسم_المادة;
"@
    
    try {
        $qdf = $access.CurrentDb.CreateQueryDef("استعلام_المواد_تحت_الحد_الأدنى", $querySQL)
        Write-Host "✓ تم إنشاء استعلام المواد تحت الحد الأدنى" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام المواد تحت الحد الأدنى موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 3. استعلام تفاصيل الوصفة مع التكلفة
    $querySQL = @"
SELECT 
    و.اسم_الوصفة,
    مك.رقم_المكون,
    م.كود_المادة,
    م.اسم_المادة,
    مك.الكمية_المطلوبة,
    وحدة.الرمز AS وحدة_القياس,
    مك.التكلفة_المعيارية,
    (مك.الكمية_المطلوبة * مك.التكلفة_المعيارية) AS إجمالي_التكلفة
FROM (((مكونات_الوصفة مك 
INNER JOIN الوصفات و ON مك.رقم_الوصفة = و.رقم_الوصفة)
INNER JOIN المواد_والمنتجات م ON مك.رقم_المادة = م.رقم_المادة)
INNER JOIN وحدات_القياس وحدة ON مك.وحدة_القياس = وحدة.رقم_الوحدة)
ORDER BY و.اسم_الوصفة, مك.رقم_المكون;
"@
    
    try {
        $qdf = $access.CurrentDb.CreateQueryDef("استعلام_تفاصيل_الوصفة", $querySQL)
        Write-Host "✓ تم إنشاء استعلام تفاصيل الوصفة" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام تفاصيل الوصفة موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 4. استعلام إجمالي تكلفة الوصفة
    $querySQL = @"
SELECT 
    و.اسم_الوصفة,
    و.كمية_الإنتاج,
    Sum(مك.الكمية_المطلوبة * مك.التكلفة_المعيارية) AS إجمالي_تكلفة_الوصفة,
    (Sum(مك.الكمية_المطلوبة * مك.التكلفة_المعيارية) / و.كمية_الإنتاج) AS تكلفة_الوحدة
FROM (مكونات_الوصفة مك 
INNER JOIN الوصفات و ON مك.رقم_الوصفة = و.رقم_الوصفة)
GROUP BY و.اسم_الوصفة, و.كمية_الإنتاج
ORDER BY و.اسم_الوصفة;
"@
    
    try {
        $qdf = $access.CurrentDb.CreateQueryDef("استعلام_تكلفة_الوصفة", $querySQL)
        Write-Host "✓ تم إنشاء استعلام تكلفة الوصفة" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام تكلفة الوصفة موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    # 5. استعلام قائمة المواد والأسعار
    $querySQL = @"
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    ف.اسم_الفئة,
    م.التكلفة_المعيارية,
    م.سعر_البيع,
    و.الرمز AS وحدة_القياس,
    م.الحد_الأدنى_للمخزون,
    م.نوع_المادة
FROM ((المواد_والمنتجات م 
INNER JOIN فئات_المواد ف ON م.رقم_الفئة = ف.رقم_الفئة)
INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة)
ORDER BY ف.اسم_الفئة, م.اسم_المادة;
"@
    
    try {
        $qdf = $access.CurrentDb.CreateQueryDef("استعلام_قائمة_المواد", $querySQL)
        Write-Host "✓ تم إنشاء استعلام قائمة المواد" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام قائمة المواد موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "إنشاء النماذج..." -ForegroundColor Cyan
    
    # إنشاء النماذج الأساسية
    try {
        # نموذج إدارة المواد والمنتجات
        $access.DoCmd.NewForm(0, "المواد_والمنتجات", "", "", 1, "نموذج_إدارة_المواد")
        $access.DoCmd.Close(2, "نموذج_إدارة_المواد", 1)
        Write-Host "✓ تم إنشاء نموذج إدارة المواد" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: نموذج إدارة المواد موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    try {
        # نموذج إدارة المستودعات
        $access.DoCmd.NewForm(0, "المستودعات", "", "", 1, "نموذج_إدارة_المستودعات")
        $access.DoCmd.Close(2, "نموذج_إدارة_المستودعات", 1)
        Write-Host "✓ تم إنشاء نموذج إدارة المستودعات" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: نموذج إدارة المستودعات موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    try {
        # نموذج إدارة الموردين
        $access.DoCmd.NewForm(0, "الموردين", "", "", 1, "نموذج_إدارة_الموردين")
        $access.DoCmd.Close(2, "نموذج_إدارة_الموردين", 1)
        Write-Host "✓ تم إنشاء نموذج إدارة الموردين" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: نموذج إدارة الموردين موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    try {
        # نموذج إدارة العملاء
        $access.DoCmd.NewForm(0, "العملاء", "", "", 1, "نموذج_إدارة_العملاء")
        $access.DoCmd.Close(2, "نموذج_إدارة_العملاء", 1)
        Write-Host "✓ تم إنشاء نموذج إدارة العملاء" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: نموذج إدارة العملاء موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    try {
        # نموذج أرصدة المخزون
        $access.DoCmd.NewForm(2, "استعلام_أرصدة_المخزون", "", "", 1, "نموذج_أرصدة_المخزون")
        $access.DoCmd.Close(2, "نموذج_أرصدة_المخزون", 1)
        Write-Host "✓ تم إنشاء نموذج أرصدة المخزون" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: نموذج أرصدة المخزون موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    try {
        # نموذج الوصفات
        $access.DoCmd.NewForm(0, "الوصفات", "", "", 1, "نموذج_إدارة_الوصفات")
        $access.DoCmd.Close(2, "نموذج_إدارة_الوصفات", 1)
        Write-Host "✓ تم إنشاء نموذج إدارة الوصفات" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: نموذج إدارة الوصفات موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "إنشاء التقارير..." -ForegroundColor Cyan
    
    # إنشاء التقارير الأساسية
    try {
        # تقرير أرصدة المخزون
        $access.DoCmd.NewReport(0, "استعلام_أرصدة_المخزون", "", "", 1, "تقرير_أرصدة_المخزون")
        $access.DoCmd.Close(3, "تقرير_أرصدة_المخزون", 1)
        Write-Host "✓ تم إنشاء تقرير أرصدة المخزون" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: تقرير أرصدة المخزون موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    try {
        # تقرير المواد تحت الحد الأدنى
        $access.DoCmd.NewReport(0, "استعلام_المواد_تحت_الحد_الأدنى", "", "", 1, "تقرير_المواد_تحت_الحد_الأدنى")
        $access.DoCmd.Close(3, "تقرير_المواد_تحت_الحد_الأدنى", 1)
        Write-Host "✓ تم إنشاء تقرير المواد تحت الحد الأدنى" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: تقرير المواد تحت الحد الأدنى موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    try {
        # تقرير تفاصيل الوصفة
        $access.DoCmd.NewReport(0, "استعلام_تفاصيل_الوصفة", "", "", 1, "تقرير_تفاصيل_الوصفة")
        $access.DoCmd.Close(3, "تقرير_تفاصيل_الوصفة", 1)
        Write-Host "✓ تم إنشاء تقرير تفاصيل الوصفة" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: تقرير تفاصيل الوصفة موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    try {
        # تقرير تكلفة الوصفة
        $access.DoCmd.NewReport(0, "استعلام_تكلفة_الوصفة", "", "", 1, "تقرير_تكلفة_الوصفة")
        $access.DoCmd.Close(3, "تقرير_تكلفة_الوصفة", 1)
        Write-Host "✓ تم إنشاء تقرير تكلفة الوصفة" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: تقرير تكلفة الوصفة موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    try {
        # تقرير قائمة المواد
        $access.DoCmd.NewReport(0, "استعلام_قائمة_المواد", "", "", 1, "تقرير_قائمة_المواد")
        $access.DoCmd.Close(3, "تقرير_قائمة_المواد", 1)
        Write-Host "✓ تم إنشاء تقرير قائمة المواد" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: تقرير قائمة المواد موجود مسبقاً أو خطأ في الإنشاء" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host "    تم إنجاز الاستعلامات والنماذج والتقارير!   " -ForegroundColor Green
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "📊 ملخص ما تم إضافته:" -ForegroundColor Cyan
    Write-Host "✓ 5 استعلامات وظيفية للتقارير والتحليل" -ForegroundColor Green
    Write-Host "✓ 6 نماذج تفاعلية لإدارة البيانات" -ForegroundColor Green
    Write-Host "✓ 5 تقارير شاملة للمتابعة والتحليل" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "🎯 الاستعلامات المنشأة:" -ForegroundColor Cyan
    Write-Host "• استعلام أرصدة المخزون" -ForegroundColor White
    Write-Host "• استعلام المواد تحت الحد الأدنى" -ForegroundColor White
    Write-Host "• استعلام تفاصيل الوصفة" -ForegroundColor White
    Write-Host "• استعلام تكلفة الوصفة" -ForegroundColor White
    Write-Host "• استعلام قائمة المواد" -ForegroundColor White
    Write-Host ""
    
    Write-Host "📝 النماذج المنشأة:" -ForegroundColor Cyan
    Write-Host "• نموذج إدارة المواد" -ForegroundColor White
    Write-Host "• نموذج إدارة المستودعات" -ForegroundColor White
    Write-Host "• نموذج إدارة الموردين" -ForegroundColor White
    Write-Host "• نموذج إدارة العملاء" -ForegroundColor White
    Write-Host "• نموذج أرصدة المخزون" -ForegroundColor White
    Write-Host "• نموذج إدارة الوصفات" -ForegroundColor White
    Write-Host ""
    
    Write-Host "📋 التقارير المنشأة:" -ForegroundColor Cyan
    Write-Host "• تقرير أرصدة المخزون" -ForegroundColor White
    Write-Host "• تقرير المواد تحت الحد الأدنى" -ForegroundColor White
    Write-Host "• تقرير تفاصيل الوصفة" -ForegroundColor White
    Write-Host "• تقرير تكلفة الوصفة" -ForegroundColor White
    Write-Host "• تقرير قائمة المواد" -ForegroundColor White
    Write-Host ""
    
} catch {
    Write-Host "خطأ في إضافة الاستعلامات والنماذج والتقارير: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # تنظيف الكائنات
    if ($access) { 
        $access.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null 
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}

Write-Host "===============================================" -ForegroundColor Magenta
Write-Host "    النظام مكتمل الآن بجميع المكونات!        " -ForegroundColor Magenta
Write-Host "===============================================" -ForegroundColor Magenta
