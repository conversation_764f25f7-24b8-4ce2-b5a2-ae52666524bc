# سكريبت إنشاء الاستعلامات والنماذج والتقارير لنظام إدارة تصنيع المعمول
# Creating Queries, Forms, and Reports for Ma'amoul Manufacturing Management System

Write-Host "بدء إنشاء الاستعلامات والنماذج والتقارير..." -ForegroundColor Green

$dbPath = "نظام_إدارة_تصنيع_المعمول.accdb"

if (-not (Test-Path $dbPath)) {
    Write-Host "خطأ: قاعدة البيانات غير موجودة. يرجى تشغيل سكريبت إنشاء النظام أولاً." -ForegroundColor Red
    exit
}

try {
    # فتح Access
    $access = New-Object -ComObject Access.Application
    $access.OpenCurrentDatabase($dbPath)
    $access.Visible = $false
    
    Write-Host "إنشاء الاستعلامات الأساسية..." -ForegroundColor Cyan
    
    # 1. استعلام أرصدة المخزون الحالية
    $querySQL = @"
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    ف.اسم_الفئة,
    مس.اسم_المستودع,
    أ.رقم_الدفعة,
    أ.تاريخ_انتهاء_الصلاحية,
    أ.الكمية_المتاحة,
    و.الرمز AS وحدة_القياس,
    أ.التكلفة_الوحدة,
    (أ.الكمية_المتاحة * أ.التكلفة_الوحدة) AS إجمالي_القيمة,
    IIf(أ.الكمية_المتاحة <= م.الحد_الأدنى_للمخزون, "تحت الحد الأدنى", "طبيعي") AS حالة_المخزون
FROM ((((أرصدة_المخزون أ 
INNER JOIN المواد_والمنتجات م ON أ.رقم_المادة = م.رقم_المادة)
INNER JOIN فئات_المواد ف ON م.رقم_الفئة = ف.رقم_الفئة)
INNER JOIN المستودعات مس ON أ.رقم_المستودع = مس.رقم_المستودع)
INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة)
WHERE أ.الكمية_المتاحة > 0
ORDER BY م.اسم_المادة, مس.اسم_المستودع;
"@
    
    $qdf = $access.CurrentDb.CreateQueryDef("استعلام_أرصدة_المخزون", $querySQL)
    Write-Host "تم إنشاء استعلام أرصدة المخزون" -ForegroundColor Green
    
    # 2. استعلام المواد تحت الحد الأدنى
    $querySQL = @"
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    ف.اسم_الفئة,
    مس.اسم_المستودع,
    Sum(أ.الكمية_المتاحة) AS إجمالي_الكمية,
    م.الحد_الأدنى_للمخزون,
    م.نقطة_إعادة_الطلب,
    و.الرمز AS وحدة_القياس
FROM ((((أرصدة_المخزون أ 
INNER JOIN المواد_والمنتجات م ON أ.رقم_المادة = م.رقم_المادة)
INNER JOIN فئات_المواد ف ON م.رقم_الفئة = ف.رقم_الفئة)
INNER JOIN المستودعات مس ON أ.رقم_المستودع = مس.رقم_المستودع)
INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة)
GROUP BY م.كود_المادة, م.اسم_المادة, ف.اسم_الفئة, مس.اسم_المستودع, 
         م.الحد_الأدنى_للمخزون, م.نقطة_إعادة_الطلب, و.الرمز
HAVING Sum(أ.الكمية_المتاحة) <= م.الحد_الأدنى_للمخزون
ORDER BY م.اسم_المادة;
"@
    
    $qdf = $access.CurrentDb.CreateQueryDef("استعلام_المواد_تحت_الحد_الأدنى", $querySQL)
    Write-Host "تم إنشاء استعلام المواد تحت الحد الأدنى" -ForegroundColor Green
    
    # 3. استعلام حركات المخزون
    $querySQL = @"
SELECT 
    ح.رقم_الحركة,
    ح.تاريخ_الحركة,
    م.كود_المادة,
    م.اسم_المادة,
    مس.اسم_المستودع,
    ح.نوع_الحركة,
    ح.رقم_المرجع,
    ح.الكمية,
    و.الرمز AS وحدة_القياس,
    ح.التكلفة_الوحدة,
    ح.إجمالي_التكلفة,
    ح.رقم_الدفعة,
    ح.المستخدم,
    ح.ملاحظات
FROM (((حركات_المخزون ح 
INNER JOIN المواد_والمنتجات م ON ح.رقم_المادة = م.رقم_المادة)
INNER JOIN المستودعات مس ON ح.رقم_المستودع = مس.رقم_المستودع)
INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة)
ORDER BY ح.تاريخ_الحركة DESC, ح.رقم_الحركة DESC;
"@
    
    $qdf = $access.CurrentDb.CreateQueryDef("استعلام_حركات_المخزون", $querySQL)
    Write-Host "تم إنشاء استعلام حركات المخزون" -ForegroundColor Green
    
    # 4. استعلام تكلفة الوصفات
    $querySQL = @"
SELECT 
    و.رقم_الوصفة,
    و.اسم_الوصفة,
    منتج.اسم_المادة AS اسم_المنتج,
    و.كمية_الإنتاج,
    وحدة_إنتاج.الرمز AS وحدة_الإنتاج,
    Sum(مك.الكمية_المطلوبة * مك.التكلفة_المعيارية) AS إجمالي_تكلفة_المواد,
    (Sum(مك.الكمية_المطلوبة * مك.التكلفة_المعيارية) / و.كمية_الإنتاج) AS تكلفة_الوحدة
FROM ((((الوصفات و 
INNER JOIN مكونات_الوصفة مك ON و.رقم_الوصفة = مك.رقم_الوصفة)
INNER JOIN المواد_والمنتجات منتج ON و.رقم_المنتج = منتج.رقم_المادة)
INNER JOIN وحدات_القياس وحدة_إنتاج ON و.وحدة_الإنتاج = وحدة_إنتاج.رقم_الوحدة)
INNER JOIN المواد_والمنتجات مادة ON مك.رقم_المادة = مادة.رقم_المادة)
WHERE و.نشط = True
GROUP BY و.رقم_الوصفة, و.اسم_الوصفة, منتج.اسم_المادة, 
         و.كمية_الإنتاج, وحدة_إنتاج.الرمز
ORDER BY و.اسم_الوصفة;
"@
    
    $qdf = $access.CurrentDb.CreateQueryDef("استعلام_تكلفة_الوصفات", $querySQL)
    Write-Host "تم إنشاء استعلام تكلفة الوصفات" -ForegroundColor Green
    
    # 5. استعلام تفاصيل الوصفة
    $querySQL = @"
SELECT 
    و.اسم_الوصفة,
    مك.رقم_المكون,
    م.كود_المادة,
    م.اسم_المادة,
    مك.الكمية_المطلوبة,
    وحدة.الرمز AS وحدة_القياس,
    مك.التكلفة_المعيارية,
    (مك.الكمية_المطلوبة * مك.التكلفة_المعيارية) AS إجمالي_التكلفة,
    مك.نسبة_الفاقد,
    مك.ملاحظات
FROM (((مكونات_الوصفة مك 
INNER JOIN الوصفات و ON مك.رقم_الوصفة = و.رقم_الوصفة)
INNER JOIN المواد_والمنتجات م ON مك.رقم_المادة = م.رقم_المادة)
INNER JOIN وحدات_القياس وحدة ON مك.وحدة_القياس = وحدة.رقم_الوحدة)
ORDER BY و.اسم_الوصفة, مك.رقم_المكون;
"@
    
    $qdf = $access.CurrentDb.CreateQueryDef("استعلام_تفاصيل_الوصفة", $querySQL)
    Write-Host "تم إنشاء استعلام تفاصيل الوصفة" -ForegroundColor Green
    
    # 6. استعلام أوامر الإنتاج
    $querySQL = @"
SELECT 
    أ.رقم_أمر_الإنتاج,
    أ.تاريخ_الأمر,
    و.اسم_الوصفة,
    منتج.اسم_المادة AS اسم_المنتج,
    أ.كمية_الإنتاج_المطلوبة,
    أ.تاريخ_البدء_المخطط,
    أ.تاريخ_الانتهاء_المخطط,
    أ.تاريخ_البدء_الفعلي,
    أ.تاريخ_الانتهاء_الفعلي,
    أ.حالة_الأمر,
    مس.اسم_المستودع,
    أ.التكلفة_المخططة,
    أ.التكلفة_الفعلية,
    أ.المسؤول,
    IIf(أ.حالة_الأمر = "مكتمل", "مكتمل", 
        IIf(أ.تاريخ_الانتهاء_المخطط < Date(), "متأخر", "في الوقت")) AS حالة_التوقيت
FROM (((أوامر_الإنتاج أ 
INNER JOIN الوصفات و ON أ.رقم_الوصفة = و.رقم_الوصفة)
INNER JOIN المواد_والمنتجات منتج ON و.رقم_المنتج = منتج.رقم_المادة)
INNER JOIN المستودعات مس ON أ.رقم_المستودع = مس.رقم_المستودع)
ORDER BY أ.تاريخ_الأمر DESC;
"@
    
    $qdf = $access.CurrentDb.CreateQueryDef("استعلام_أوامر_الإنتاج", $querySQL)
    Write-Host "تم إنشاء استعلام أوامر الإنتاج" -ForegroundColor Green
    
    # 7. استعلام المواد منتهية الصلاحية
    $querySQL = @"
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    مس.اسم_المستودع,
    أ.رقم_الدفعة,
    أ.تاريخ_انتهاء_الصلاحية,
    أ.الكمية_المتاحة,
    و.الرمز AS وحدة_القياس,
    (أ.الكمية_المتاحة * أ.التكلفة_الوحدة) AS قيمة_المخزون_المنتهي,
    DateDiff("d", Date(), أ.تاريخ_انتهاء_الصلاحية) AS أيام_متبقية
FROM (((أرصدة_المخزون أ 
INNER JOIN المواد_والمنتجات م ON أ.رقم_المادة = م.رقم_المادة)
INNER JOIN المستودعات مس ON أ.رقم_المستودع = مس.رقم_المستودع)
INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة)
WHERE أ.تاريخ_انتهاء_الصلاحية IS NOT NULL 
  AND أ.تاريخ_انتهاء_الصلاحية <= DateAdd("d", 30, Date())
  AND أ.الكمية_المتاحة > 0
ORDER BY أ.تاريخ_انتهاء_الصلاحية;
"@
    
    $qdf = $access.CurrentDb.CreateQueryDef("استعلام_المواد_منتهية_الصلاحية", $querySQL)
    Write-Host "تم إنشاء استعلام المواد منتهية الصلاحية" -ForegroundColor Green
    
    Write-Host "تم إنشاء جميع الاستعلامات بنجاح!" -ForegroundColor Green
    
} catch {
    Write-Host "خطأ في إنشاء الاستعلامات: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # تنظيف الكائنات
    if ($access) { 
        $access.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null 
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}

Write-Host "انتهى إنشاء الاستعلامات. الخطوة التالية: إنشاء النماذج والتقارير..." -ForegroundColor Yellow
