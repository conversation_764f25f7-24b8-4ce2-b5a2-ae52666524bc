# سكريبت إنشاء الاستعلامات والنماذج والتقارير النهائي
# Final Script for Creating Queries, Forms, and Reports

Write-Host "بدء إنشاء الاستعلامات والنماذج والتقارير..." -ForegroundColor Green

$dbPath = "نظام_إدارة_تصنيع_المعمول.accdb"

if (-not (Test-Path $dbPath)) {
    Write-Host "خطأ: قاعدة البيانات غير موجودة." -ForegroundColor Red
    exit
}

try {
    # فتح Access
    $access = New-Object -ComObject Access.Application
    $access.OpenCurrentDatabase($dbPath)
    $access.Visible = $false
    
    Write-Host "إنشاء الاستعلامات..." -ForegroundColor Cyan
    
    # 1. استعلام أرصدة المخزون
    $querySQL = @"
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    ف.اسم_الفئة,
    مس.اسم_المستودع,
    أ.رقم_الدفعة,
    أ.تاريخ_انتهاء_الصلاحية,
    أ.الكمية_المتاحة,
    و.الرمز AS وحدة_القياس,
    أ.التكلفة_الوحدة,
    (أ.الكمية_المتاحة * أ.التكلفة_الوحدة) AS إجمالي_القيمة
FROM ((((أرصدة_المخزون أ 
INNER JOIN المواد_والمنتجات م ON أ.رقم_المادة = م.رقم_المادة)
INNER JOIN فئات_المواد ف ON م.رقم_الفئة = ف.رقم_الفئة)
INNER JOIN المستودعات مس ON أ.رقم_المستودع = مس.رقم_المستودع)
INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة)
WHERE أ.الكمية_المتاحة > 0
ORDER BY م.اسم_المادة, مس.اسم_المستودع;
"@
    
    try {
        $qdf = $access.CurrentDb.CreateQueryDef("استعلام_أرصدة_المخزون", $querySQL)
        Write-Host "تم إنشاء استعلام أرصدة المخزون" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام أرصدة المخزون موجود مسبقاً" -ForegroundColor Yellow
    }
    
    # 2. استعلام المواد تحت الحد الأدنى
    $querySQL = @"
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    ف.اسم_الفئة,
    Sum(أ.الكمية_المتاحة) AS إجمالي_الكمية,
    م.الحد_الأدنى_للمخزون,
    م.نقطة_إعادة_الطلب,
    و.الرمز AS وحدة_القياس
FROM (((أرصدة_المخزون أ 
INNER JOIN المواد_والمنتجات م ON أ.رقم_المادة = م.رقم_المادة)
INNER JOIN فئات_المواد ف ON م.رقم_الفئة = ف.رقم_الفئة)
INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة)
GROUP BY م.كود_المادة, م.اسم_المادة, ف.اسم_الفئة, 
         م.الحد_الأدنى_للمخزون, م.نقطة_إعادة_الطلب, و.الرمز
HAVING Sum(أ.الكمية_المتاحة) <= م.الحد_الأدنى_للمخزون
ORDER BY م.اسم_المادة;
"@
    
    try {
        $qdf = $access.CurrentDb.CreateQueryDef("استعلام_المواد_تحت_الحد_الأدنى", $querySQL)
        Write-Host "تم إنشاء استعلام المواد تحت الحد الأدنى" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام المواد تحت الحد الأدنى موجود مسبقاً" -ForegroundColor Yellow
    }
    
    # 3. استعلام حركات المخزون
    $querySQL = @"
SELECT 
    ح.رقم_الحركة,
    ح.تاريخ_الحركة,
    م.كود_المادة,
    م.اسم_المادة,
    مس.اسم_المستودع,
    ح.نوع_الحركة,
    ح.رقم_المرجع,
    ح.الكمية,
    و.الرمز AS وحدة_القياس,
    ح.التكلفة_الوحدة,
    ح.إجمالي_التكلفة,
    ح.رقم_الدفعة,
    ح.المستخدم,
    ح.ملاحظات
FROM (((حركات_المخزون ح 
INNER JOIN المواد_والمنتجات م ON ح.رقم_المادة = م.رقم_المادة)
INNER JOIN المستودعات مس ON ح.رقم_المستودع = مس.رقم_المستودع)
INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة)
ORDER BY ح.تاريخ_الحركة DESC, ح.رقم_الحركة DESC;
"@
    
    try {
        $qdf = $access.CurrentDb.CreateQueryDef("استعلام_حركات_المخزون", $querySQL)
        Write-Host "تم إنشاء استعلام حركات المخزون" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام حركات المخزون موجود مسبقاً" -ForegroundColor Yellow
    }
    
    # 4. استعلام تفاصيل الوصفة
    $querySQL = @"
SELECT 
    و.اسم_الوصفة,
    مك.رقم_المكون,
    م.كود_المادة,
    م.اسم_المادة,
    مك.الكمية_المطلوبة,
    وحدة.الرمز AS وحدة_القياس,
    مك.التكلفة_المعيارية,
    (مك.الكمية_المطلوبة * مك.التكلفة_المعيارية) AS إجمالي_التكلفة,
    مك.نسبة_الفاقد,
    مك.ملاحظات
FROM (((مكونات_الوصفة مك 
INNER JOIN الوصفات و ON مك.رقم_الوصفة = و.رقم_الوصفة)
INNER JOIN المواد_والمنتجات م ON مك.رقم_المادة = م.رقم_المادة)
INNER JOIN وحدات_القياس وحدة ON مك.وحدة_القياس = وحدة.رقم_الوحدة)
ORDER BY و.اسم_الوصفة, مك.رقم_المكون;
"@
    
    try {
        $qdf = $access.CurrentDb.CreateQueryDef("استعلام_تفاصيل_الوصفة", $querySQL)
        Write-Host "تم إنشاء استعلام تفاصيل الوصفة" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: استعلام تفاصيل الوصفة موجود مسبقاً" -ForegroundColor Yellow
    }
    
    Write-Host "إنشاء النماذج..." -ForegroundColor Cyan
    
    # إنشاء النماذج الأساسية
    try {
        # نموذج إدارة المواد والمنتجات
        $access.DoCmd.NewForm(0, "المواد_والمنتجات", "", "", 1, "نموذج_إدارة_المواد")
        $access.DoCmd.Close(2, "نموذج_إدارة_المواد", 1)
        Write-Host "تم إنشاء نموذج إدارة المواد" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: نموذج إدارة المواد موجود مسبقاً" -ForegroundColor Yellow
    }
    
    try {
        # نموذج إدارة المستودعات
        $access.DoCmd.NewForm(0, "المستودعات", "", "", 1, "نموذج_إدارة_المستودعات")
        $access.DoCmd.Close(2, "نموذج_إدارة_المستودعات", 1)
        Write-Host "تم إنشاء نموذج إدارة المستودعات" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: نموذج إدارة المستودعات موجود مسبقاً" -ForegroundColor Yellow
    }
    
    try {
        # نموذج إدارة الموردين
        $access.DoCmd.NewForm(0, "الموردين", "", "", 1, "نموذج_إدارة_الموردين")
        $access.DoCmd.Close(2, "نموذج_إدارة_الموردين", 1)
        Write-Host "تم إنشاء نموذج إدارة الموردين" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: نموذج إدارة الموردين موجود مسبقاً" -ForegroundColor Yellow
    }
    
    try {
        # نموذج إدارة العملاء
        $access.DoCmd.NewForm(0, "العملاء", "", "", 1, "نموذج_إدارة_العملاء")
        $access.DoCmd.Close(2, "نموذج_إدارة_العملاء", 1)
        Write-Host "تم إنشاء نموذج إدارة العملاء" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: نموذج إدارة العملاء موجود مسبقاً" -ForegroundColor Yellow
    }
    
    try {
        # نموذج أرصدة المخزون
        $access.DoCmd.NewForm(2, "استعلام_أرصدة_المخزون", "", "", 1, "نموذج_أرصدة_المخزون")
        $access.DoCmd.Close(2, "نموذج_أرصدة_المخزون", 1)
        Write-Host "تم إنشاء نموذج أرصدة المخزون" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: نموذج أرصدة المخزون موجود مسبقاً" -ForegroundColor Yellow
    }
    
    try {
        # نموذج المواد تحت الحد الأدنى
        $access.DoCmd.NewForm(2, "استعلام_المواد_تحت_الحد_الأدنى", "", "", 1, "نموذج_المواد_تحت_الحد_الأدنى")
        $access.DoCmd.Close(2, "نموذج_المواد_تحت_الحد_الأدنى", 1)
        Write-Host "تم إنشاء نموذج المواد تحت الحد الأدنى" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: نموذج المواد تحت الحد الأدنى موجود مسبقاً" -ForegroundColor Yellow
    }
    
    Write-Host "إنشاء التقارير..." -ForegroundColor Cyan
    
    # إنشاء التقارير الأساسية
    try {
        # تقرير أرصدة المخزون
        $access.DoCmd.NewReport(0, "استعلام_أرصدة_المخزون", "", "", 1, "تقرير_أرصدة_المخزون")
        $access.DoCmd.Close(3, "تقرير_أرصدة_المخزون", 1)
        Write-Host "تم إنشاء تقرير أرصدة المخزون" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: تقرير أرصدة المخزون موجود مسبقاً" -ForegroundColor Yellow
    }
    
    try {
        # تقرير المواد تحت الحد الأدنى
        $access.DoCmd.NewReport(0, "استعلام_المواد_تحت_الحد_الأدنى", "", "", 1, "تقرير_المواد_تحت_الحد_الأدنى")
        $access.DoCmd.Close(3, "تقرير_المواد_تحت_الحد_الأدنى", 1)
        Write-Host "تم إنشاء تقرير المواد تحت الحد الأدنى" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: تقرير المواد تحت الحد الأدنى موجود مسبقاً" -ForegroundColor Yellow
    }
    
    try {
        # تقرير حركات المخزون
        $access.DoCmd.NewReport(0, "استعلام_حركات_المخزون", "", "", 1, "تقرير_حركات_المخزون")
        $access.DoCmd.Close(3, "تقرير_حركات_المخزون", 1)
        Write-Host "تم إنشاء تقرير حركات المخزون" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: تقرير حركات المخزون موجود مسبقاً" -ForegroundColor Yellow
    }
    
    try {
        # تقرير تفاصيل الوصفة
        $access.DoCmd.NewReport(0, "استعلام_تفاصيل_الوصفة", "", "", 1, "تقرير_تفاصيل_الوصفة")
        $access.DoCmd.Close(3, "تقرير_تفاصيل_الوصفة", 1)
        Write-Host "تم إنشاء تقرير تفاصيل الوصفة" -ForegroundColor Green
    } catch {
        Write-Host "تحذير: تقرير تفاصيل الوصفة موجود مسبقاً" -ForegroundColor Yellow
    }
    
    Write-Host "تم إنشاء جميع الاستعلامات والنماذج والتقارير بنجاح!" -ForegroundColor Green
    
} catch {
    Write-Host "خطأ في إنشاء الاستعلامات والنماذج والتقارير: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # تنظيف الكائنات
    if ($access) { 
        $access.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null 
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}

Write-Host "انتهى إنشاء الاستعلامات والنماذج والتقارير بنجاح!" -ForegroundColor Yellow
