# سكريبت إدراج البيانات التجريبية الكاملة لنظام إدارة تصنيع المعمول
# Inserting Complete Sample Data for Ma'amoul Manufacturing Management System

Write-Host "بدء إدراج البيانات التجريبية الكاملة..." -ForegroundColor Green

$dbPath = "نظام_إدارة_تصنيع_المعمول.accdb"

if (-not (Test-Path $dbPath)) {
    Write-Host "خطأ: قاعدة البيانات غير موجودة. يرجى تشغيل سكريبت إنشاء النظام أولاً." -ForegroundColor Red
    exit
}

try {
    # إنشاء كائن DAO
    $dao = New-Object -ComObject DAO.DBEngine.120
    $db = $dao.OpenDatabase($dbPath)
    
    Write-Host "إدراج الموردين..." -ForegroundColor Cyan
    
    # إدراج الموردين
    $db.Execute("INSERT INTO الموردين (اسم_المورد, العنوان, الهاتف, البريد_الإلكتروني, شخص_الاتصال, شروط_الدفع) VALUES ('شركة المكسرات المصرية', 'القاهرة - مدينة نصر', '01234567890', '<EMAIL>', 'أحمد محمود', 'نقدي عند التسليم')")
    $db.Execute("INSERT INTO الموردين (اسم_المورد, العنوان, الهاتف, البريد_الإلكتروني, شخص_الاتصال, شروط_الدفع) VALUES ('مطاحن الدقيق الحديثة', 'الجيزة - 6 أكتوبر', '01098765432', '<EMAIL>', 'فاطمة علي', '30 يوم')")
    $db.Execute("INSERT INTO الموردين (اسم_المورد, العنوان, الهاتف, البريد_الإلكتروني, شخص_الاتصال, شروط_الدفع) VALUES ('مناحل العسل الطبيعي', 'الفيوم - سنورس', '01156789012', '<EMAIL>', 'محمد حسن', 'نقدي عند التسليم')")
    $db.Execute("INSERT INTO الموردين (اسم_المورد, العنوان, الهاتف, البريد_الإلكتروني, شخص_الاتصال, شروط_الدفع) VALUES ('شركة الزيوت النباتية', 'الإسكندرية - برج العرب', '01223456789', '<EMAIL>', 'سارة أحمد', '15 يوم')")
    $db.Execute("INSERT INTO الموردين (اسم_المورد, العنوان, الهاتف, البريد_الإلكتروني, شخص_الاتصال, شروط_الدفع) VALUES ('مصنع التعبئة والتغليف', 'القليوبية - شبرا الخيمة', '01087654321', '<EMAIL>', 'خالد عبدالله', '45 يوم')")
    
    Write-Host "إدراج العملاء..." -ForegroundColor Cyan
    
    # إدراج العملاء
    $db.Execute("INSERT INTO العملاء (اسم_العميل, العنوان, الهاتف, البريد_الإلكتروني, شخص_الاتصال, حد_الائتمان) VALUES ('سوبر ماركت الأهرام', 'القاهرة - مصر الجديدة', '0227654321', '<EMAIL>', 'نادر محمد', 50000)")
    $db.Execute("INSERT INTO العملاء (اسم_العميل, العنوان, الهاتف, البريد_الإلكتروني, شخص_الاتصال, حد_الائتمان) VALUES ('هايبر ماركت كارفور', 'الجيزة - المهندسين', '0233456789', '<EMAIL>', 'مريم أحمد', 100000)")
    $db.Execute("INSERT INTO العملاء (اسم_العميل, العنوان, الهاتف, البريد_الإلكتروني, شخص_الاتصال, حد_الائتمان) VALUES ('متاجر سبينيس', 'القاهرة - التجمع الخامس', '0226789012', '<EMAIL>', 'أمير سالم', 75000)")
    $db.Execute("INSERT INTO العملاء (اسم_العميل, العنوان, الهاتف, البريد_الإلكتروني, شخص_الاتصال, حد_الائتمان) VALUES ('محلات العثيم', 'الإسكندرية - سموحة', '0334567890', '<EMAIL>', 'ليلى حسن', 60000)")
    $db.Execute("INSERT INTO العملاء (اسم_العميل, العنوان, الهاتف, البريد_الإلكتروني, شخص_الاتصال, حد_الائتمان) VALUES ('بيع بالتجزئة مباشر', 'متنوع', '01012345678', '<EMAIL>', 'مبيعات مباشرة', 10000)")
    
    Write-Host "إدراج المواد والمنتجات..." -ForegroundColor Cyan
    
    # إدراج المواد الخام
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, الحد_الأقصى_للمخزون, نقطة_إعادة_الطلب, مدة_الصلاحية_بالأيام, نوع_المادة) VALUES ('NUT001', 'جوز مقشر درجة أولى', 1, 1, 180.00, 50, 500, 100, 365, 'مادة خام')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, الحد_الأقصى_للمخزون, نقطة_إعادة_الطلب, مدة_الصلاحية_بالأيام, نوع_المادة) VALUES ('FLR001', 'دقيق أبيض فاخر', 2, 1, 12.50, 100, 1000, 200, 180, 'مادة خام')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, الحد_الأقصى_للمخزون, نقطة_إعادة_الطلب, مدة_الصلاحية_بالأيام, نوع_المادة) VALUES ('HON001', 'عسل نحل طبيعي', 3, 1, 85.00, 20, 200, 50, 730, 'مادة خام')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, الحد_الأقصى_للمخزون, نقطة_إعادة_الطلب, مدة_الصلاحية_بالأيام, نوع_المادة) VALUES ('BUT001', 'زبدة طبيعية', 4, 1, 45.00, 30, 300, 75, 90, 'مادة خام')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, الحد_الأقصى_للمخزون, نقطة_إعادة_الطلب, مدة_الصلاحية_بالأيام, نوع_المادة) VALUES ('SUG001', 'سكر أبيض ناعم', 3, 1, 18.00, 50, 500, 125, 365, 'مادة خام')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, الحد_الأقصى_للمخزون, نقطة_إعادة_الطلب, مدة_الصلاحية_بالأيام, نوع_المادة) VALUES ('CIN001', 'قرفة مطحونة', 5, 2, 120.00, 5, 50, 15, 365, 'مادة خام')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, الحد_الأقصى_للمخزون, نقطة_إعادة_الطلب, مدة_الصلاحية_بالأيام, نوع_المادة) VALUES ('VAN001', 'فانيليا سائلة', 5, 5, 25.00, 10, 100, 25, 730, 'مادة خام')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, الحد_الأقصى_للمخزون, نقطة_إعادة_الطلب, مدة_الصلاحية_بالأيام, نوع_المادة) VALUES ('BOX001', 'علبة كرتون 600 جرام', 6, 3, 2.50, 500, 5000, 1000, 1095, 'مادة تعبئة')")
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, الحد_الأدنى_للمخزون, الحد_الأقصى_للمخزون, نقطة_إعادة_الطلب, مدة_الصلاحية_بالأيام, نوع_المادة) VALUES ('LAB001', 'ملصق المنتج', 6, 3, 0.15, 1000, 10000, 2000, 1095, 'مادة تعبئة')")
    
    # إدراج المنتج النهائي
    $db.Execute("INSERT INTO المواد_والمنتجات (كود_المادة, اسم_المادة, رقم_الفئة, وحدة_القياس_الأساسية, التكلفة_المعيارية, سعر_البيع, الحد_الأدنى_للمخزون, الحد_الأقصى_للمخزون, نقطة_إعادة_الطلب, مدة_الصلاحية_بالأيام, نوع_المادة) VALUES ('PRD001', 'معمول الجوز والعسل 600 جرام', 7, 4, 24.65, 85.00, 50, 1000, 100, 45, 'منتج نهائي')")
    
    Write-Host "إدراج مراكز التكلفة..." -ForegroundColor Cyan
    
    # إدراج مراكز التكلفة
    $db.Execute("INSERT INTO مراكز_التكلفة (اسم_مركز_التكلفة, نوع_المركز, معدل_التكلفة_ساعة) VALUES ('خط الإنتاج الأول', 'إنتاج', 25.00)")
    $db.Execute("INSERT INTO مراكز_التكلفة (اسم_مركز_التكلفة, نوع_المركز, معدل_التكلفة_ساعة) VALUES ('قسم التعبئة والتغليف', 'تعبئة', 15.00)")
    $db.Execute("INSERT INTO مراكز_التكلفة (اسم_مركز_التكلفة, نوع_المركز, معدل_التكلفة_ساعة) VALUES ('مراقبة الجودة', 'جودة', 30.00)")
    $db.Execute("INSERT INTO مراكز_التكلفة (اسم_مركز_التكلفة, نوع_المركز, معدل_التكلفة_ساعة) VALUES ('الصيانة والنظافة', 'خدمات', 20.00)")
    
    Write-Host "إنشاء الوصفة..." -ForegroundColor Cyan
    
    # إنشاء وصفة معمول الجوز والعسل
    $db.Execute("INSERT INTO الوصفات (رقم_المنتج, اسم_الوصفة, كمية_الإنتاج, وحدة_الإنتاج, تاريخ_الإنشاء, تاريخ_آخر_تعديل) VALUES (11, 'وصفة معمول الجوز والعسل - 100 قطعة', 100, 3, #" + (Get-Date).ToString("MM/dd/yyyy") + "#, #" + (Get-Date).ToString("MM/dd/yyyy") + "#)")
    
    # إدراج مكونات الوصفة
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية, نسبة_الفاقد) VALUES (1, 1, 8.0, 1, 180.00, 2.0)")  # جوز
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية, نسبة_الفاقد) VALUES (1, 2, 15.0, 1, 12.50, 1.0)")  # دقيق
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية, نسبة_الفاقد) VALUES (1, 3, 3.5, 1, 85.00, 0.5)")  # عسل
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية, نسبة_الفاقد) VALUES (1, 4, 2.0, 1, 45.00, 1.0)")  # زبدة
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية, نسبة_الفاقد) VALUES (1, 5, 1.5, 1, 18.00, 0.0)")  # سكر
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية, نسبة_الفاقد) VALUES (1, 6, 50.0, 2, 120.00, 0.0)")  # قرفة
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية, نسبة_الفاقد) VALUES (1, 7, 0.1, 5, 25.00, 0.0)")  # فانيليا
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية, نسبة_الفاقد) VALUES (1, 8, 100.0, 3, 2.50, 0.0)")  # علب
    $db.Execute("INSERT INTO مكونات_الوصفة (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, وحدة_القياس, التكلفة_المعيارية, نسبة_الفاقد) VALUES (1, 9, 100.0, 3, 0.15, 0.0)")  # ملصقات
    
    Write-Host "إدراج أرصدة المخزون الافتتاحية..." -ForegroundColor Cyan
    
    # إدراج أرصدة المخزون الافتتاحية
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, تاريخ_الإنتاج, تاريخ_انتهاء_الصلاحية, الكمية_المتاحة, التكلفة_الوحدة) VALUES (1, 1, 'NUT-2024-001', #01/15/2024#, #01/15/2025#, 200.0, 180.00)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, تاريخ_الإنتاج, تاريخ_انتهاء_الصلاحية, الكمية_المتاحة, التكلفة_الوحدة) VALUES (2, 1, 'FLR-2024-001', #02/01/2024#, #08/01/2024#, 500.0, 12.50)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, تاريخ_الإنتاج, تاريخ_انتهاء_الصلاحية, الكمية_المتاحة, التكلفة_الوحدة) VALUES (3, 1, 'HON-2024-001', #01/10/2024#, #01/10/2026#, 100.0, 85.00)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, تاريخ_الإنتاج, تاريخ_انتهاء_الصلاحية, الكمية_المتاحة, التكلفة_الوحدة) VALUES (4, 1, 'BUT-2024-001', #02/15/2024#, #05/15/2024#, 150.0, 45.00)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, تاريخ_الإنتاج, تاريخ_انتهاء_الصلاحية, الكمية_المتاحة, التكلفة_الوحدة) VALUES (5, 1, 'SUG-2024-001', #01/20/2024#, #01/20/2025#, 300.0, 18.00)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, تاريخ_الإنتاج, تاريخ_انتهاء_الصلاحية, الكمية_المتاحة, التكلفة_الوحدة) VALUES (6, 1, 'CIN-2024-001', #01/05/2024#, #01/05/2025#, 25.0, 120.00)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, تاريخ_الإنتاج, تاريخ_انتهاء_الصلاحية, الكمية_المتاحة, التكلفة_الوحدة) VALUES (7, 1, 'VAN-2024-001', #01/08/2024#, #01/08/2026#, 50.0, 25.00)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, تاريخ_الإنتاج, تاريخ_انتهاء_الصلاحية, الكمية_المتاحة, التكلفة_الوحدة) VALUES (8, 3, 'BOX-2024-001', #01/01/2024#, #01/01/2027#, 2000.0, 2.50)")
    $db.Execute("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المستودع, رقم_الدفعة, تاريخ_الإنتاج, تاريخ_انتهاء_الصلاحية, الكمية_المتاحة, التكلفة_الوحدة) VALUES (9, 3, 'LAB-2024-001', #01/01/2024#, #01/01/2027#, 5000.0, 0.15)")
    
    Write-Host "إنشاء أمر إنتاج تجريبي..." -ForegroundColor Cyan
    
    # إنشاء أمر إنتاج
    $db.Execute("INSERT INTO أوامر_الإنتاج (رقم_الوصفة, كمية_الإنتاج_المطلوبة, تاريخ_الأمر, تاريخ_البدء_المخطط, تاريخ_الانتهاء_المخطط, حالة_الأمر, رقم_المستودع, التكلفة_المخططة, المسؤول) VALUES (1, 100, #" + (Get-Date).ToString("MM/dd/yyyy") + "#, #" + (Get-Date).AddDays(1).ToString("MM/dd/yyyy") + "#, #" + (Get-Date).AddDays(3).ToString("MM/dd/yyyy") + "#, 'مخطط', 2, 2464.65, 'مدير الإنتاج')")
    
    Write-Host "تم إدراج جميع البيانات التجريبية بنجاح!" -ForegroundColor Green
    
} catch {
    Write-Host "خطأ في إدراج البيانات: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # تنظيف الكائنات
    if ($db) { $db.Close() }
    if ($dao) { [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dao) | Out-Null }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}

Write-Host "انتهى إدراج البيانات التجريبية بنجاح!" -ForegroundColor Yellow
