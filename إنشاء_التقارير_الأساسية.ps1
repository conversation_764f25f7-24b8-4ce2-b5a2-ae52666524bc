# سكريپت إنشاء التقارير الأساسية
# تاريخ الإنشاء: 2025-09-19

Write-Host "بدء إنشاء التقارير الأساسية..." -ForegroundColor Green

try {
    $dbPath = Join-Path (Get-Location) "نظام_إدارة_تصنيع_المعمول.accdb"
    
    # فتح قاعدة البيانات
    $dao = New-Object -ComObject DAO.DBEngine.120
    $db = $dao.OpenDatabase($dbPath)
    
    Write-Host "إنشاء الاستعلامات للتقارير..." -ForegroundColor Cyan
    
    # استعلام تقرير أرصدة المخزون
    $sql = @"
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    م.نوع_المادة,
    ف.اسم_الفئة,
    و.اسم_الوحدة,
    أ.الكمية_المتاحة,
    أ.متوسط_التكلفة,
    (أ.الكمية_المتاحة * أ.متوسط_التكلفة) AS القيمة_الإجمالية,
    م.الحد_الأدنى_للمخزون,
    م.نقطة_إعادة_الطلب,
    IIf(أ.الكمية_المتاحة <= م.الحد_الأدنى_للمخزون, "نعم", "لا") AS تحت_الحد_الأدنى
FROM ((المواد_والمنتجات م 
    INNER JOIN أرصدة_المخزون أ ON م.رقم_المادة = أ.رقم_المادة)
    INNER JOIN فئات_المواد ف ON م.رقم_الفئة = ف.رقم_الفئة)
    INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة
ORDER BY م.نوع_المادة, ف.اسم_الفئة, م.اسم_المادة;
"@
    
    $qd = $db.CreateQueryDef("استعلام_أرصدة_المخزون", $sql)
    Write-Host "تم إنشاء استعلام أرصدة المخزون" -ForegroundColor Green
    
    # استعلام تقرير حركات المخزون
    $sql = @"
SELECT 
    ح.تاريخ_الحركة,
    م.كود_المادة,
    م.اسم_المادة,
    ح.نوع_الحركة,
    ح.الكمية,
    و.اسم_الوحدة,
    ح.تكلفة_الوحدة,
    (ح.الكمية * ح.تكلفة_الوحدة) AS القيمة_الإجمالية,
    ح.رقم_المرجع,
    ح.ملاحظات,
    ح.المستخدم
FROM (حركات_المخزون ح 
    INNER JOIN المواد_والمنتجات م ON ح.رقم_المادة = م.رقم_المادة)
    INNER JOIN وحدات_القياس و ON ح.وحدة_القياس = و.رقم_الوحدة
ORDER BY ح.تاريخ_الحركة DESC, ح.رقم_الحركة DESC;
"@
    
    $qd = $db.CreateQueryDef("استعلام_حركات_المخزون", $sql)
    Write-Host "تم إنشاء استعلام حركات المخزون" -ForegroundColor Green
    
    # استعلام تقرير تكلفة الوصفات
    $sql = @"
SELECT 
    و.رقم_الوصفة,
    و.اسم_الوصفة,
    م.اسم_المادة AS اسم_المنتج,
    و.كمية_الإنتاج,
    وق.اسم_الوحدة AS وحدة_الإنتاج,
    و.تكلفة_العمالة_للوحدة,
    و.التكاليف_الإضافية,
    Sum(مو.الكمية_المطلوبة * مو.التكلفة_المعيارية) AS تكلفة_المواد,
    (Sum(مو.الكمية_المطلوبة * مو.التكلفة_المعيارية) + (و.كمية_الإنتاج * و.تكلفة_العمالة_للوحدة) + و.التكاليف_الإضافية) AS إجمالي_التكلفة,
    ((Sum(مو.الكمية_المطلوبة * مو.التكلفة_المعيارية) + (و.كمية_الإنتاج * و.تكلفة_العمالة_للوحدة) + و.التكاليف_الإضافية) / و.كمية_الإنتاج) AS تكلفة_الوحدة
FROM ((الوصفات و 
    INNER JOIN مكونات_الوصفة مو ON و.رقم_الوصفة = مو.رقم_الوصفة)
    INNER JOIN المواد_والمنتجات م ON و.رقم_المنتج = م.رقم_المادة)
    INNER JOIN وحدات_القياس وق ON و.وحدة_الإنتاج = وق.رقم_الوحدة
GROUP BY و.رقم_الوصفة, و.اسم_الوصفة, م.اسم_المادة, و.كمية_الإنتاج, وق.اسم_الوحدة, و.تكلفة_العمالة_للوحدة, و.التكاليف_الإضافية
ORDER BY و.رقم_الوصفة;
"@
    
    $qd = $db.CreateQueryDef("استعلام_تكلفة_الوصفات", $sql)
    Write-Host "تم إنشاء استعلام تكلفة الوصفات" -ForegroundColor Green
    
    # استعلام تقرير مكونات الوصفة
    $sql = @"
SELECT 
    و.اسم_الوصفة,
    مو.ترتيب_الإضافة,
    م.كود_المادة,
    م.اسم_المادة,
    مو.الكمية_المطلوبة,
    وق.اسم_الوحدة,
    مو.التكلفة_المعيارية,
    (مو.الكمية_المطلوبة * مو.التكلفة_المعيارية) AS التكلفة_الإجمالية,
    مو.نسبة_الفاقد,
    مو.مرحلة_الإضافة,
    مو.ملاحظات
FROM ((الوصفات و 
    INNER JOIN مكونات_الوصفة مو ON و.رقم_الوصفة = مو.رقم_الوصفة)
    INNER JOIN المواد_والمنتجات م ON مو.رقم_المادة = م.رقم_المادة)
    INNER JOIN وحدات_القياس وق ON مو.وحدة_القياس = وق.رقم_الوحدة
ORDER BY و.رقم_الوصفة, مو.ترتيب_الإضافة;
"@
    
    $qd = $db.CreateQueryDef("استعلام_مكونات_الوصفة", $sql)
    Write-Host "تم إنشاء استعلام مكونات الوصفة" -ForegroundColor Green
    
    # استعلام تقرير المواد تحت الحد الأدنى
    $sql = @"
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    م.نوع_المادة,
    أ.الكمية_المتاحة,
    و.اسم_الوحدة,
    م.الحد_الأدنى_للمخزون,
    م.نقطة_إعادة_الطلب,
    (م.نقطة_إعادة_الطلب - أ.الكمية_المتاحة) AS الكمية_المطلوبة,
    مر.اسم_المورد,
    مر.رقم_الهاتف
FROM (((المواد_والمنتجات م 
    INNER JOIN أرصدة_المخزون أ ON م.رقم_المادة = أ.رقم_المادة)
    INNER JOIN وحدات_القياس و ON م.وحدة_القياس_الأساسية = و.رقم_الوحدة)
    LEFT JOIN الموردين مر ON م.المورد_الرئيسي = مر.رقم_المورد)
WHERE أ.الكمية_المتاحة <= م.الحد_الأدنى_للمخزون
ORDER BY م.نوع_المادة, (م.نقطة_إعادة_الطلب - أ.الكمية_المتاحة) DESC;
"@
    
    $qd = $db.CreateQueryDef("استعلام_المواد_تحت_الحد_الأدنى", $sql)
    Write-Host "تم إنشاء استعلام المواد تحت الحد الأدنى" -ForegroundColor Green
    
    # استعلام تقرير قيمة المخزون حسب الفئة
    $sql = @"
SELECT 
    ف.اسم_الفئة,
    م.نوع_المادة,
    Count(م.رقم_المادة) AS عدد_المواد,
    Sum(أ.الكمية_المتاحة * أ.متوسط_التكلفة) AS القيمة_الإجمالية,
    Avg(أ.متوسط_التكلفة) AS متوسط_التكلفة
FROM ((المواد_والمنتجات م 
    INNER JOIN أرصدة_المخزون أ ON م.رقم_المادة = أ.رقم_المادة)
    INNER JOIN فئات_المواد ف ON م.رقم_الفئة = ف.رقم_الفئة)
GROUP BY ف.اسم_الفئة, م.نوع_المادة
ORDER BY ف.اسم_الفئة, م.نوع_المادة;
"@
    
    $qd = $db.CreateQueryDef("استعلام_قيمة_المخزون_حسب_الفئة", $sql)
    Write-Host "تم إنشاء استعلام قيمة المخزون حسب الفئة" -ForegroundColor Green
    
    Write-Host "تم إنشاء جميع الاستعلامات بنجاح!" -ForegroundColor Green
    Write-Host "يمكنك الآن استخدام هذه الاستعلامات لإنشاء التقارير في Access" -ForegroundColor Cyan
    
    # إغلاق قاعدة البيانات
    $db.Close()
    
} catch {
    Write-Host "حدث خطأ: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($db) { try { $db.Close() } catch {} }
    if ($dao) {
        try {
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dao) | Out-Null
        } catch {}
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}
