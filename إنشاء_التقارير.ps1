# سكريبت إنشاء التقارير لنظام إدارة تصنيع المعمول
# Creating Reports for Ma'amoul Manufacturing Management System

Write-Host "بدء إنشاء التقارير..." -ForegroundColor Green

$dbPath = "نظام_إدارة_تصنيع_المعمول.accdb"

if (-not (Test-Path $dbPath)) {
    Write-Host "خطأ: قاعدة البيانات غير موجودة. يرجى تشغيل سكريبت إنشاء النظام أولاً." -ForegroundColor Red
    exit
}

try {
    # فتح Access
    $access = New-Object -ComObject Access.Application
    $access.OpenCurrentDatabase($dbPath)
    $access.Visible = $false
    
    Write-Host "إنشاء التقارير الأساسية..." -ForegroundColor Cyan
    
    # 1. تقرير أرصدة المخزون
    $access.DoCmd.OpenReport("استعلام_أرصدة_المخزون", 0, "", "", 1, 1)
    $rpt = $access.Reports("استعلام_أرصدة_المخزون")
    
    $rpt.Caption = "تقرير أرصدة المخزون"
    $rpt.RecordSource = "استعلام_أرصدة_المخزون"
    
    $access.DoCmd.Close(3, "استعلام_أرصدة_المخزون", 1)  # Save and close report
    $access.DoCmd.Rename("تقرير_أرصدة_المخزون", 3, "استعلام_أرصدة_المخزون")
    Write-Host "تم إنشاء تقرير أرصدة المخزون" -ForegroundColor Green
    
    # 2. تقرير المواد تحت الحد الأدنى
    $access.DoCmd.OpenReport("استعلام_المواد_تحت_الحد_الأدنى", 0, "", "", 1, 1)
    $rpt = $access.Reports("استعلام_المواد_تحت_الحد_الأدنى")
    
    $rpt.Caption = "تقرير المواد تحت الحد الأدنى"
    $rpt.RecordSource = "استعلام_المواد_تحت_الحد_الأدنى"
    
    $access.DoCmd.Close(3, "استعلام_المواد_تحت_الحد_الأدنى", 1)
    $access.DoCmd.Rename("تقرير_المواد_تحت_الحد_الأدنى", 3, "استعلام_المواد_تحت_الحد_الأدنى")
    Write-Host "تم إنشاء تقرير المواد تحت الحد الأدنى" -ForegroundColor Green
    
    # 3. تقرير حركات المخزون
    $access.DoCmd.OpenReport("استعلام_حركات_المخزون", 0, "", "", 1, 1)
    $rpt = $access.Reports("استعلام_حركات_المخزون")
    
    $rpt.Caption = "تقرير حركات المخزون"
    $rpt.RecordSource = "استعلام_حركات_المخزون"
    
    $access.DoCmd.Close(3, "استعلام_حركات_المخزون", 1)
    $access.DoCmd.Rename("تقرير_حركات_المخزون", 3, "استعلام_حركات_المخزون")
    Write-Host "تم إنشاء تقرير حركات المخزون" -ForegroundColor Green
    
    # 4. تقرير تكلفة الوصفات
    $access.DoCmd.OpenReport("استعلام_تكلفة_الوصفات", 0, "", "", 1, 1)
    $rpt = $access.Reports("استعلام_تكلفة_الوصفات")
    
    $rpt.Caption = "تقرير تحليل تكلفة الوصفات"
    $rpt.RecordSource = "استعلام_تكلفة_الوصفات"
    
    $access.DoCmd.Close(3, "استعلام_تكلفة_الوصفات", 1)
    $access.DoCmd.Rename("تقرير_تكلفة_الوصفات", 3, "استعلام_تكلفة_الوصفات")
    Write-Host "تم إنشاء تقرير تكلفة الوصفات" -ForegroundColor Green
    
    # 5. تقرير تفاصيل الوصفة
    $access.DoCmd.OpenReport("استعلام_تفاصيل_الوصفة", 0, "", "", 1, 1)
    $rpt = $access.Reports("استعلام_تفاصيل_الوصفة")
    
    $rpt.Caption = "تقرير تفاصيل الوصفة"
    $rpt.RecordSource = "استعلام_تفاصيل_الوصفة"
    
    $access.DoCmd.Close(3, "استعلام_تفاصيل_الوصفة", 1)
    $access.DoCmd.Rename("تقرير_تفاصيل_الوصفة", 3, "استعلام_تفاصيل_الوصفة")
    Write-Host "تم إنشاء تقرير تفاصيل الوصفة" -ForegroundColor Green
    
    # 6. تقرير أوامر الإنتاج
    $access.DoCmd.OpenReport("استعلام_أوامر_الإنتاج", 0, "", "", 1, 1)
    $rpt = $access.Reports("استعلام_أوامر_الإنتاج")
    
    $rpt.Caption = "تقرير أوامر الإنتاج"
    $rpt.RecordSource = "استعلام_أوامر_الإنتاج"
    
    $access.DoCmd.Close(3, "استعلام_أوامر_الإنتاج", 1)
    $access.DoCmd.Rename("تقرير_أوامر_الإنتاج", 3, "استعلام_أوامر_الإنتاج")
    Write-Host "تم إنشاء تقرير أوامر الإنتاج" -ForegroundColor Green
    
    # 7. تقرير المواد منتهية الصلاحية
    $access.DoCmd.OpenReport("استعلام_المواد_منتهية_الصلاحية", 0, "", "", 1, 1)
    $rpt = $access.Reports("استعلام_المواد_منتهية_الصلاحية")
    
    $rpt.Caption = "تقرير المواد منتهية الصلاحية"
    $rpt.RecordSource = "استعلام_المواد_منتهية_الصلاحية"
    
    $access.DoCmd.Close(3, "استعلام_المواد_منتهية_الصلاحية", 1)
    $access.DoCmd.Rename("تقرير_المواد_منتهية_الصلاحية", 3, "استعلام_المواد_منتهية_الصلاحية")
    Write-Host "تم إنشاء تقرير المواد منتهية الصلاحية" -ForegroundColor Green
    
    # إنشاء تقارير إضافية متقدمة
    Write-Host "إنشاء التقارير المتقدمة..." -ForegroundColor Cyan
    
    # 8. تقرير ملخص المخزون حسب الفئة
    $querySQL = @"
SELECT 
    ف.اسم_الفئة,
    Count(م.رقم_المادة) AS عدد_المواد,
    Sum(أ.الكمية_المتاحة * أ.التكلفة_الوحدة) AS إجمالي_قيمة_المخزون,
    Avg(أ.التكلفة_الوحدة) AS متوسط_التكلفة
FROM ((أرصدة_المخزون أ 
INNER JOIN المواد_والمنتجات م ON أ.رقم_المادة = م.رقم_المادة)
INNER JOIN فئات_المواد ف ON م.رقم_الفئة = ف.رقم_الفئة)
WHERE أ.الكمية_المتاحة > 0
GROUP BY ف.اسم_الفئة
ORDER BY Sum(أ.الكمية_المتاحة * أ.التكلفة_الوحدة) DESC;
"@
    
    $qdf = $access.CurrentDb.CreateQueryDef("استعلام_ملخص_المخزون_حسب_الفئة", $querySQL)
    
    $access.DoCmd.OpenReport("استعلام_ملخص_المخزون_حسب_الفئة", 0, "", "", 1, 1)
    $rpt = $access.Reports("استعلام_ملخص_المخزون_حسب_الفئة")
    
    $rpt.Caption = "تقرير ملخص المخزون حسب الفئة"
    $rpt.RecordSource = "استعلام_ملخص_المخزون_حسب_الفئة"
    
    $access.DoCmd.Close(3, "استعلام_ملخص_المخزون_حسب_الفئة", 1)
    $access.DoCmd.Rename("تقرير_ملخص_المخزون_حسب_الفئة", 3, "استعلام_ملخص_المخزون_حسب_الفئة")
    Write-Host "تم إنشاء تقرير ملخص المخزون حسب الفئة" -ForegroundColor Green
    
    # 9. تقرير حركات المخزون الشهرية
    $querySQL = @"
SELECT 
    Format(ح.تاريخ_الحركة, "yyyy-mm") AS الشهر,
    ح.نوع_الحركة,
    Count(ح.رقم_الحركة) AS عدد_الحركات,
    Sum(ح.إجمالي_التكلفة) AS إجمالي_القيمة
FROM حركات_المخزون ح
WHERE ح.تاريخ_الحركة >= DateAdd("m", -12, Date())
GROUP BY Format(ح.تاريخ_الحركة, "yyyy-mm"), ح.نوع_الحركة
ORDER BY Format(ح.تاريخ_الحركة, "yyyy-mm") DESC, ح.نوع_الحركة;
"@
    
    $qdf = $access.CurrentDb.CreateQueryDef("استعلام_حركات_المخزون_الشهرية", $querySQL)
    
    $access.DoCmd.OpenReport("استعلام_حركات_المخزون_الشهرية", 0, "", "", 1, 1)
    $rpt = $access.Reports("استعلام_حركات_المخزون_الشهرية")
    
    $rpt.Caption = "تقرير حركات المخزون الشهرية"
    $rpt.RecordSource = "استعلام_حركات_المخزون_الشهرية"
    
    $access.DoCmd.Close(3, "استعلام_حركات_المخزون_الشهرية", 1)
    $access.DoCmd.Rename("تقرير_حركات_المخزون_الشهرية", 3, "استعلام_حركات_المخزون_الشهرية")
    Write-Host "تم إنشاء تقرير حركات المخزون الشهرية" -ForegroundColor Green
    
    # 10. تقرير أداء الإنتاج
    $querySQL = @"
SELECT 
    أ.رقم_أمر_الإنتاج,
    و.اسم_الوصفة,
    أ.كمية_الإنتاج_المطلوبة,
    أ.تاريخ_البدء_المخطط,
    أ.تاريخ_الانتهاء_المخطط,
    أ.تاريخ_البدء_الفعلي,
    أ.تاريخ_الانتهاء_الفعلي,
    أ.التكلفة_المخططة,
    أ.التكلفة_الفعلية,
    (أ.التكلفة_الفعلية - أ.التكلفة_المخططة) AS انحراف_التكلفة,
    DateDiff("d", أ.تاريخ_الانتهاء_المخطط, أ.تاريخ_الانتهاء_الفعلي) AS انحراف_الوقت_بالأيام,
    أ.حالة_الأمر
FROM (أوامر_الإنتاج أ 
INNER JOIN الوصفات و ON أ.رقم_الوصفة = و.رقم_الوصفة)
WHERE أ.تاريخ_الأمر >= DateAdd("m", -6, Date())
ORDER BY أ.تاريخ_الأمر DESC;
"@
    
    $qdf = $access.CurrentDb.CreateQueryDef("استعلام_أداء_الإنتاج", $querySQL)
    
    $access.DoCmd.OpenReport("استعلام_أداء_الإنتاج", 0, "", "", 1, 1)
    $rpt = $access.Reports("استعلام_أداء_الإنتاج")
    
    $rpt.Caption = "تقرير أداء الإنتاج"
    $rpt.RecordSource = "استعلام_أداء_الإنتاج"
    
    $access.DoCmd.Close(3, "استعلام_أداء_الإنتاج", 1)
    $access.DoCmd.Rename("تقرير_أداء_الإنتاج", 3, "استعلام_أداء_الإنتاج")
    Write-Host "تم إنشاء تقرير أداء الإنتاج" -ForegroundColor Green
    
    Write-Host "تم إنشاء جميع التقارير بنجاح!" -ForegroundColor Green
    
} catch {
    Write-Host "خطأ في إنشاء التقارير: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # تنظيف الكائنات
    if ($access) { 
        $access.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null 
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}

Write-Host "انتهى إنشاء التقارير بنجاح!" -ForegroundColor Yellow
