# تشغيل المثال التطبيقي الكامل لنظام إدارة تصنيع المعمول
# يتضمن إنشاء قاعدة البيانات كاملة ثم تشغيل المثال

Write-Host "=== بدء إنشاء نظام إدارة تصنيع المعمول الكامل ===" -ForegroundColor Cyan

try {
    # الخطوة 1: إنشاء قاعدة البيانات الأساسية
    Write-Host "الخطوة 1: إنشاء قاعدة البيانات الأساسية..." -ForegroundColor Yellow
    & ".\إنشاء_قاعدة_البيانات_محسن.ps1"

    # الخطوة 2: إكمال الجداول
    Write-Host "الخطوة 2: إكمال إنشاء الجداول..." -ForegroundColor Yellow
    & ".\إكمال_الجداول.ps1"

    # الخطوة 3: إنشاء جداول التصنيع
    Write-Host "الخطوة 3: إنشاء جداول التصنيع..." -ForegroundColor Yellow
    & ".\إنشاء_جداول_التصنيع.ps1"

    # الخطوة 4: إدراج البيانات الأساسية
    Write-Host "الخطوة 4: إدراج البيانات الأساسية..." -ForegroundColor Yellow
    & ".\إدراج_البيانات_الأساسية.ps1"

    # الخطوة 5: إنشاء الوصفة والمثال
    Write-Host "الخطوة 5: إنشاء الوصفة والمثال..." -ForegroundColor Yellow
    & ".\إنشاء_الوصفة_والمثال.ps1"

    # الخطوة 6: إنشاء النماذج الأساسية
    Write-Host "الخطوة 6: إنشاء النماذج الأساسية..." -ForegroundColor Yellow
    & ".\إنشاء_النماذج_الأساسية.ps1"

    # الخطوة 7: إنشاء التقارير الأساسية
    Write-Host "الخطوة 7: إنشاء التقارير الأساسية..." -ForegroundColor Yellow
    & ".\إنشاء_التقارير_الأساسية.ps1"
    
    Write-Host "=== تم إنشاء النظام بنجاح! ===" -ForegroundColor Green
    Write-Host ""
    Write-Host "=== بدء المثال التطبيقي الكامل ===" -ForegroundColor Cyan
    
    # تشغيل المثال التطبيقي
    $dbPath = "نظام_إدارة_تصنيع_المعمول.accdb"
    
    # التحقق من وجود قاعدة البيانات
    if (-not (Test-Path $dbPath)) {
        throw "لم يتم العثور على ملف قاعدة البيانات: $dbPath"
    }
    
    # إنشاء اتصال بقاعدة البيانات
    $dbEngine = New-Object -ComObject DAO.DBEngine.120
    $db = $dbEngine.OpenDatabase($dbPath)
    
    Write-Host "تم الاتصال بقاعدة البيانات بنجاح" -ForegroundColor Green
    Write-Host "سيناريو: إنتاج 100 قطعة من معمول الجوز والعسل 600 جرام" -ForegroundColor White
    Write-Host ""
    
    # الخطوة 1: إضافة أرصدة مخزون أولية
    Write-Host "=== الخطوة 1: إضافة أرصدة مخزون أولية ===" -ForegroundColor Yellow
    
    $rs = $db.OpenRecordset("أرصدة_المخزون")
    
    # دقيق قمح - 500 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 1
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 500.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 8.50
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()
    
    # جوز مقشر - 100 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 2
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 100.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 45.00
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()
    
    # عسل نحل - 50 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 3
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 50.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 80.00
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()
    
    # زبدة طبيعية - 80 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 4
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 80.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 25.00
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()
    
    # سكر ناعم - 200 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 5
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 200.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 12.00
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()
    
    # بيكنج باودر - 5000 جرام
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 6
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 5000.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 0.015
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()
    
    # ملح طعام - 10000 جرام
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 7
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 10000.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 0.003
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()
    
    # علب كرتونية - 2000 قطعة
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 8
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 2000.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 2.50
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()
    
    $rs.Close()
    Write-Host "تم إضافة أرصدة المخزون الأولية" -ForegroundColor Green
    
    Write-Host "=== الخطوة 2: تسجيل حركات الشراء ===" -ForegroundColor Yellow
    
    # تسجيل حركات الشراء
    $rs = $db.OpenRecordset("حركات_المخزون")
    
    # شراء دقيق قمح
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 1
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "وارد شراء"
    $rs.Fields("الكمية").Value = 500.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 8.50
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PO-001"
    $rs.Fields("ملاحظات").Value = "شراء دقيق قمح عالي الجودة"
    $rs.Fields("المستخدم").Value = "مدير المشتريات"
    $rs.Update()
    
    # شراء جوز مقشر
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 2
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "وارد شراء"
    $rs.Fields("الكمية").Value = 100.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 45.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PO-002"
    $rs.Fields("ملاحظات").Value = "شراء جوز مقشر طازج"
    $rs.Fields("المستخدم").Value = "مدير المشتريات"
    $rs.Update()
    
    # شراء عسل نحل
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 3
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "وارد شراء"
    $rs.Fields("الكمية").Value = 50.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 80.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PO-003"
    $rs.Fields("ملاحظات").Value = "شراء عسل نحل طبيعي خالص"
    $rs.Fields("المستخدم").Value = "مدير المشتريات"
    $rs.Update()
    
    # شراء زبدة طبيعية
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 4
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "وارد شراء"
    $rs.Fields("الكمية").Value = 80.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 25.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PO-004"
    $rs.Fields("ملاحظات").Value = "شراء زبدة طبيعية عالية الجودة"
    $rs.Fields("المستخدم").Value = "مدير المشتريات"
    $rs.Update()
    
    # شراء سكر ناعم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 5
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "وارد شراء"
    $rs.Fields("الكمية").Value = 200.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 12.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PO-005"
    $rs.Fields("ملاحظات").Value = "شراء سكر أبيض ناعم"
    $rs.Fields("المستخدم").Value = "مدير المشتريات"
    $rs.Update()
    
    # شراء علب كرتونية
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 8
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "وارد شراء"
    $rs.Fields("الكمية").Value = 2000.0
    $rs.Fields("وحدة_القياس").Value = 3
    $rs.Fields("تكلفة_الوحدة").Value = 2.50
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PO-006"
    $rs.Fields("ملاحظات").Value = "شراء علب كرتونية للتعبئة"
    $rs.Fields("المستخدم").Value = "مدير المشتريات"
    $rs.Update()
    
    $rs.Close()
    Write-Host "تم تسجيل حركات الشراء" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "=== تم إنشاء النظام وتشغيل المثال بنجاح! ===" -ForegroundColor Green
    Write-Host "يمكنك الآن فتح ملف قاعدة البيانات: نظام_إدارة_تصنيع_المعمول.accdb" -ForegroundColor Cyan
    
    # إغلاق الاتصال
    $db.Close()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($db) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dbEngine) | Out-Null
    
} catch {
    Write-Host "حدث خطأ: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
