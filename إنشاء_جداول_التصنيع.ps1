# سكريپت إنشاء جداول التصنيع ومحاسبة التكاليف
# تاريخ الإنشاء: 2025-09-19

Write-Host "بدء إنشاء جداول التصنيع ومحاسبة التكاليف..." -ForegroundColor Green

try {
    $dbPath = Join-<PERSON> (Get-Location) "نظام_إدارة_تصنيع_المعمول.accdb"
    
    # فتح قاعدة البيانات
    $dao = New-Object -ComObject DAO.DBEngine.120
    $db = $dao.OpenDatabase($dbPath)
    
    # جدول أرصدة المخزون
    Write-Host "إنشاء جدول أرصدة المخزون..." -ForegroundColor Cyan
    $tabledef = $db.CreateTableDef("أرصدة_المخزون")
    
    $field = $tabledef.CreateField("رقم_الرصيد", 4) # dbLong
    $field.Attributes = 16 # dbAutoIncrField
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("رقم_المادة", 4) # dbLong
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("رقم_المستودع", 4) # dbLong
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("رقم_الدفعة", 10, 50) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_الصلاحية", 8) # dbDate
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("الكمية_المتاحة", 7) # dbDouble
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("الكمية_المحجوزة", 7) # dbDouble
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("الكمية_المطلوبة", 7) # dbDouble
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("التكلفة_الوحدة", 5) # dbCurrency
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("إجمالي_القيمة", 5) # dbCurrency
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_آخر_حركة", 8) # dbDate
    $field.DefaultValue = "Now()"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("ملاحظات", 12) # dbMemo
    $tabledef.Fields.Append($field)
    
    # المفتاح الأساسي
    $index = $tabledef.CreateIndex("PrimaryKey")
    $index.Primary = $true
    $indexfield = $index.CreateField("رقم_الرصيد")
    $index.Fields.Append($indexfield)
    $tabledef.Indexes.Append($index)
    
    $db.TableDefs.Append($tabledef)
    Write-Host "تم إنشاء جدول أرصدة المخزون" -ForegroundColor Green
    
    # جدول حركات المخزون
    Write-Host "إنشاء جدول حركات المخزون..." -ForegroundColor Cyan
    $tabledef = $db.CreateTableDef("حركات_المخزون")
    
    $field = $tabledef.CreateField("رقم_الحركة", 4) # dbLong
    $field.Attributes = 16 # dbAutoIncrField
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("رقم_المادة", 4) # dbLong
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("رقم_المستودع", 4) # dbLong
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("نوع_الحركة", 10, 50) # dbText
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("رقم_المستند", 10, 50) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_الحركة", 8) # dbDate
    $field.DefaultValue = "Now()"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("رقم_الدفعة", 10, 50) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_الصلاحية", 8) # dbDate
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("الكمية", 7) # dbDouble
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تكلفة_الوحدة", 5) # dbCurrency
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("إجمالي_التكلفة", 5) # dbCurrency
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("الرصيد_بعد_الحركة", 7) # dbDouble
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("متوسط_التكلفة_بعد_الحركة", 5) # dbCurrency
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("مرجع_المستند", 10, 100) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("ملاحظات", 12) # dbMemo
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("المستخدم", 10, 50) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_الإدخال", 8) # dbDate
    $field.DefaultValue = "Now()"
    $tabledef.Fields.Append($field)
    
    # المفتاح الأساسي
    $index = $tabledef.CreateIndex("PrimaryKey")
    $index.Primary = $true
    $indexfield = $index.CreateField("رقم_الحركة")
    $index.Fields.Append($indexfield)
    $tabledef.Indexes.Append($index)
    
    $db.TableDefs.Append($tabledef)
    Write-Host "تم إنشاء جدول حركات المخزون" -ForegroundColor Green
    
    # جدول الوصفات
    Write-Host "إنشاء جدول الوصفات..." -ForegroundColor Cyan
    $tabledef = $db.CreateTableDef("الوصفات")
    
    $field = $tabledef.CreateField("رقم_الوصفة", 4) # dbLong
    $field.Attributes = 16 # dbAutoIncrField
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("رقم_المنتج", 4) # dbLong
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("رقم_إصدار_الوصفة", 10, 20) # dbText
    $field.DefaultValue = "1.0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("اسم_الوصفة", 10, 200) # dbText
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("وصف_الوصفة", 12) # dbMemo
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("كمية_الإنتاج", 7) # dbDouble
    $field.Required = $true
    $field.DefaultValue = "1"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("وحدة_الإنتاج", 4) # dbLong
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("مدة_الإنتاج_بالدقائق", 4) # dbLong
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("عدد_العمال_المطلوب", 4) # dbLong
    $field.DefaultValue = "1"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تكلفة_العمالة_للوحدة", 5) # dbCurrency
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("التكاليف_الإضافية", 5) # dbCurrency
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_بداية_الصلاحية", 8) # dbDate
    $field.DefaultValue = "Date()"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_نهاية_الصلاحية", 8) # dbDate
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("حالة_الوصفة", 10, 20) # dbText
    $field.DefaultValue = "نشطة"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("ملاحظات", 12) # dbMemo
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_الإنشاء", 8) # dbDate
    $field.DefaultValue = "Now()"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_آخر_تحديث", 8) # dbDate
    $field.DefaultValue = "Now()"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("المستخدم", 10, 50) # dbText
    $tabledef.Fields.Append($field)
    
    # المفتاح الأساسي
    $index = $tabledef.CreateIndex("PrimaryKey")
    $index.Primary = $true
    $indexfield = $index.CreateField("رقم_الوصفة")
    $index.Fields.Append($indexfield)
    $tabledef.Indexes.Append($index)
    
    $db.TableDefs.Append($tabledef)
    Write-Host "تم إنشاء جدول الوصفات" -ForegroundColor Green
    
    # جدول مكونات الوصفة
    Write-Host "إنشاء جدول مكونات الوصفة..." -ForegroundColor Cyan
    $tabledef = $db.CreateTableDef("مكونات_الوصفة")
    
    $field = $tabledef.CreateField("رقم_المكون", 4) # dbLong
    $field.Attributes = 16 # dbAutoIncrField
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("رقم_الوصفة", 4) # dbLong
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("رقم_المادة", 4) # dbLong
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("الكمية_المطلوبة", 7) # dbDouble
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("وحدة_القياس", 4) # dbLong
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("نسبة_الفاقد", 7) # dbDouble
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("التكلفة_المعيارية", 5) # dbCurrency
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("ملاحظات", 12) # dbMemo
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("ترتيب_الإضافة", 4) # dbLong
    $field.DefaultValue = "1"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("مرحلة_الإضافة", 10, 50) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("إجباري", 1) # dbBoolean
    $field.DefaultValue = "True"
    $tabledef.Fields.Append($field)
    
    # المفتاح الأساسي
    $index = $tabledef.CreateIndex("PrimaryKey")
    $index.Primary = $true
    $indexfield = $index.CreateField("رقم_المكون")
    $index.Fields.Append($indexfield)
    $tabledef.Indexes.Append($index)
    
    $db.TableDefs.Append($tabledef)
    Write-Host "تم إنشاء جدول مكونات الوصفة" -ForegroundColor Green
    
    # جدول مراكز التكلفة
    Write-Host "إنشاء جدول مراكز التكلفة..." -ForegroundColor Cyan
    $tabledef = $db.CreateTableDef("مراكز_التكلفة")
    
    $field = $tabledef.CreateField("رقم_مركز_التكلفة", 4) # dbLong
    $field.Attributes = 16 # dbAutoIncrField
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("كود_مركز_التكلفة", 10, 50) # dbText
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("اسم_مركز_التكلفة", 10, 200) # dbText
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("نوع_مركز_التكلفة", 10, 50) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("وصف_المركز", 12) # dbMemo
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("معدل_التحميل_للساعة", 5) # dbCurrency
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("التكاليف_الثابتة_الشهرية", 5) # dbCurrency
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("السعة_الإنتاجية_الشهرية", 7) # dbDouble
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("وحدة_السعة", 10, 50) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("المسؤول", 10, 100) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("ملاحظات", 12) # dbMemo
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_الإنشاء", 8) # dbDate
    $field.DefaultValue = "Now()"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("نشط", 1) # dbBoolean
    $field.DefaultValue = "True"
    $tabledef.Fields.Append($field)
    
    # المفتاح الأساسي
    $index = $tabledef.CreateIndex("PrimaryKey")
    $index.Primary = $true
    $indexfield = $index.CreateField("رقم_مركز_التكلفة")
    $index.Fields.Append($indexfield)
    $tabledef.Indexes.Append($index)
    
    # فهرس فريد لكود مركز التكلفة
    $index = $tabledef.CreateIndex("كود_مركز_التكلفة_فريد")
    $index.Unique = $true
    $indexfield = $index.CreateField("كود_مركز_التكلفة")
    $index.Fields.Append($indexfield)
    $tabledef.Indexes.Append($index)
    
    $db.TableDefs.Append($tabledef)
    Write-Host "تم إنشاء جدول مراكز التكلفة" -ForegroundColor Green
    
    Write-Host "تم إكمال جداول التصنيع ومحاسبة التكاليف بنجاح!" -ForegroundColor Green
    
    # إغلاق قاعدة البيانات
    $db.Close()
    
} catch {
    Write-Host "حدث خطأ: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($db) { try { $db.Close() } catch {} }
    if ($dao) {
        try {
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dao) | Out-Null
        } catch {}
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}
