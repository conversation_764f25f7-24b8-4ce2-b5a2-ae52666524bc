# سكريبت PowerShell لإنشاء قاعدة بيانات Access لنظام إدارة تصنيع المعمول
# تاريخ الإنشاء: 2025-09-19

Write-Host "بدء إنشاء قاعدة بيانات نظام إدارة تصنيع المعمول..." -ForegroundColor Green

try {
    # إنشاء كائن Access
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    
    # مسار قاعدة البيانات الجديدة
    $dbPath = Join-Path (Get-Location) "نظام_إدارة_تصنيع_المعمول.accdb"
    
    # حذف قاعدة البيانات إذا كانت موجودة
    if (Test-Path $dbPath) {
        Remove-Item $dbPath -Force
        Write-Host "تم حذف قاعدة البيانات القديمة" -ForegroundColor Yellow
    }
    
    # إنشاء قاعدة بيانات جديدة
    $db = $access.DBEngine.CreateDatabase($dbPath, ";LANGID=0x0401;CP=1256;COUNTRY=0")
    Write-Host "تم إنشاء قاعدة البيانات بنجاح: $dbPath" -ForegroundColor Green
    
    # فتح قاعدة البيانات
    $access.OpenCurrentDatabase($dbPath)
    
    # إنشاء الجداول
    Write-Host "بدء إنشاء الجداول..." -ForegroundColor Cyan
    
    # جدول وحدات القياس
    $sql = @"
CREATE TABLE وحدات_القياس (
    رقم_الوحدة AUTOINCREMENT PRIMARY KEY,
    اسم_الوحدة TEXT(50) NOT NULL,
    اختصار_الوحدة TEXT(10) NOT NULL,
    نوع_الوحدة TEXT(20) NOT NULL,
    معامل_التحويل_للوحدة_الأساسية DOUBLE DEFAULT 1,
    الوحدة_الأساسية YESNO DEFAULT False,
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YESNO DEFAULT True
);
"@
    $access.DoCmd.RunSQL($sql)
    Write-Host "تم إنشاء جدول وحدات القياس" -ForegroundColor Green
    
    # جدول المستودعات
    $sql = @"
CREATE TABLE المستودعات (
    رقم_المستودع AUTOINCREMENT PRIMARY KEY,
    اسم_المستودع TEXT(100) NOT NULL,
    موقع_المستودع TEXT(200),
    نوع_المستودع TEXT(50),
    مسؤول_المستودع TEXT(100),
    هاتف_المسؤول TEXT(20),
    السعة_القصوى DOUBLE,
    وحدة_السعة TEXT(20),
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YESNO DEFAULT True
);
"@
    $access.DoCmd.RunSQL($sql)
    Write-Host "تم إنشاء جدول المستودعات" -ForegroundColor Green
    
    # جدول فئات المواد
    $sql = @"
CREATE TABLE فئات_المواد (
    رقم_الفئة AUTOINCREMENT PRIMARY KEY,
    اسم_الفئة TEXT(100) NOT NULL,
    وصف_الفئة MEMO,
    فئة_رئيسية LONG,
    رمز_الفئة TEXT(20),
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YESNO DEFAULT True
);
"@
    $access.DoCmd.RunSQL($sql)
    Write-Host "تم إنشاء جدول فئات المواد" -ForegroundColor Green
    
    # جدول المواد والمنتجات
    $sql = @"
CREATE TABLE المواد_والمنتجات (
    رقم_المادة AUTOINCREMENT PRIMARY KEY,
    كود_المادة TEXT(50) NOT NULL,
    اسم_المادة TEXT(200) NOT NULL,
    وصف_المادة MEMO,
    رقم_الفئة LONG NOT NULL,
    نوع_المادة TEXT(50) NOT NULL,
    وحدة_القياس_الأساسية LONG NOT NULL,
    وحدة_الشراء LONG,
    وحدة_البيع LONG,
    وحدة_الإنتاج LONG,
    الحد_الأدنى_للمخزون DOUBLE DEFAULT 0,
    الحد_الأقصى_للمخزون DOUBLE DEFAULT 0,
    نقطة_إعادة_الطلب DOUBLE DEFAULT 0,
    مدة_الصلاحية_بالأيام LONG DEFAULT 0,
    يتطلب_تتبع_الدفعات YESNO DEFAULT False,
    يتطلب_تتبع_الصلاحية YESNO DEFAULT False,
    التكلفة_المعيارية CURRENCY DEFAULT 0,
    سعر_البيع_المقترح CURRENCY DEFAULT 0,
    معدل_الضريبة DOUBLE DEFAULT 0,
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    تاريخ_آخر_تحديث DATETIME DEFAULT Now(),
    نشط YESNO DEFAULT True
);
"@
    $access.DoCmd.RunSQL($sql)
    Write-Host "تم إنشاء جدول المواد والمنتجات" -ForegroundColor Green
    
    # جدول الموردين
    $sql = @"
CREATE TABLE الموردين (
    رقم_المورد AUTOINCREMENT PRIMARY KEY,
    كود_المورد TEXT(50) NOT NULL,
    اسم_المورد TEXT(200) NOT NULL,
    اسم_جهة_الاتصال TEXT(100),
    العنوان MEMO,
    المدينة TEXT(100),
    المحافظة TEXT(100),
    الرمز_البريدي TEXT(20),
    الهاتف TEXT(50),
    الفاكس TEXT(50),
    البريد_الإلكتروني TEXT(100),
    الموقع_الإلكتروني TEXT(200),
    شروط_الدفع TEXT(100),
    مدة_التوريد_بالأيام LONG DEFAULT 0,
    حد_الائتمان CURRENCY DEFAULT 0,
    معدل_الخصم DOUBLE DEFAULT 0,
    تقييم_المورد TEXT(20),
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    تاريخ_آخر_تحديث DATETIME DEFAULT Now(),
    نشط YESNO DEFAULT True
);
"@
    $access.DoCmd.RunSQL($sql)
    Write-Host "تم إنشاء جدول الموردين" -ForegroundColor Green
    
    # جدول العملاء
    $sql = @"
CREATE TABLE العملاء (
    رقم_العميل AUTOINCREMENT PRIMARY KEY,
    كود_العميل TEXT(50) NOT NULL,
    اسم_العميل TEXT(200) NOT NULL,
    نوع_العميل TEXT(50),
    اسم_جهة_الاتصال TEXT(100),
    العنوان MEMO,
    المدينة TEXT(100),
    المحافظة TEXT(100),
    الرمز_البريدي TEXT(20),
    الهاتف TEXT(50),
    الفاكس TEXT(50),
    البريد_الإلكتروني TEXT(100),
    الموقع_الإلكتروني TEXT(200),
    شروط_الدفع TEXT(100),
    حد_الائتمان CURRENCY DEFAULT 0,
    معدل_الخصم DOUBLE DEFAULT 0,
    تصنيف_العميل TEXT(20),
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    تاريخ_آخر_تحديث DATETIME DEFAULT Now(),
    نشط YESNO DEFAULT True
);
"@
    $access.DoCmd.RunSQL($sql)
    Write-Host "تم إنشاء جدول العملاء" -ForegroundColor Green
    
    Write-Host "تم إنشاء الجداول الأساسية بنجاح!" -ForegroundColor Green
    Write-Host "سيتم إنشاء باقي الجداول في المرحلة التالية..." -ForegroundColor Yellow
    
    # حفظ وإغلاق قاعدة البيانات
    $access.DoCmd.Save()
    $access.CloseCurrentDatabase()
    $access.Quit()
    
    Write-Host "تم إنشاء قاعدة البيانات بنجاح!" -ForegroundColor Green
    Write-Host "مسار الملف: $dbPath" -ForegroundColor Cyan
    
} catch {
    Write-Host "حدث خطأ أثناء إنشاء قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # تنظيف الكائنات
    if ($access) {
        try {
            $access.Quit()
        } catch {}
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}
