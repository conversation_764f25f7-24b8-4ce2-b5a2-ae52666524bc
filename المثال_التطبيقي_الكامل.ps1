# المثال التطبيقي الكامل - إنتاج 100 قطعة معمول الجوز والعسل
# تاريخ الإنشاء: 2025-09-19

Write-Host "بدء المثال التطبيقي الكامل..." -ForegroundColor Green
Write-Host "سيناريو: إنتاج 100 قطعة من معمول الجوز والعسل 600 جرام" -ForegroundColor Cyan

try {
    $dbPath = Join-Path (Get-Location) "نظام_إدارة_تصنيع_المعمول.accdb"
    
    # فتح قاعدة البيانات
    $dao = New-Object -ComObject DAO.DBEngine.120
    $db = $dao.OpenDatabase($dbPath)
    
    Write-Host "=== الخطوة 1: إضافة أرصدة مخزون أولية ===" -ForegroundColor Yellow
    
    # إضافة أرصدة المخزون الأولية
    $rs = $db.OpenRecordset("أرصدة_المخزون")
    
    # دقيق القمح - 500 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 1
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 500.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 8.50
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()

    # جوز مقشر - 100 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 2
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 100.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 45.00
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()

    # عسل نحل - 50 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 3
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 50.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 80.00
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()

    # زبدة طبيعية - 80 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 4
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 80.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 25.00
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()

    # سكر ناعم - 200 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 5
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 200.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 12.00
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()

    # بيكنج باودر - 5000 جرام
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 6
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 5000.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 0.015
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()

    # ملح طعام - 10000 جرام
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 7
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 10000.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 0.003
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()

    # علب كرتونية - 2000 قطعة
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 8
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 2000.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 2.50
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()
    
    $rs.Close()
    Write-Host "تم إضافة أرصدة المخزون الأولية" -ForegroundColor Green
    
    Write-Host "=== الخطوة 2: تسجيل حركات الشراء ===" -ForegroundColor Yellow
    
    # تسجيل حركات شراء المواد الخام
    $rs = $db.OpenRecordset("حركات_المخزون")
    
    # شراء دقيق القمح
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 1
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "وارد شراء"
    $rs.Fields("الكمية").Value = 500.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 8.50
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PO-001"
    $rs.Fields("ملاحظات").Value = "شراء دقيق قمح عالي الجودة"
    $rs.Fields("المستخدم").Value = "مدير المشتريات"
    $rs.Update()

    # شراء جوز مقشر
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 2
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "وارد شراء"
    $rs.Fields("الكمية").Value = 100.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 45.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PO-002"
    $rs.Fields("ملاحظات").Value = "شراء جوز مقشر طازج"
    $rs.Fields("المستخدم").Value = "مدير المشتريات"
    $rs.Update()

    # شراء عسل نحل
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 3
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "وارد شراء"
    $rs.Fields("الكمية").Value = 50.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 80.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PO-003"
    $rs.Fields("ملاحظات").Value = "شراء عسل نحل طبيعي خالص"
    $rs.Fields("المستخدم").Value = "مدير المشتريات"
    $rs.Update()

    # شراء زبدة طبيعية
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 4
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "وارد شراء"
    $rs.Fields("الكمية").Value = 80.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 25.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PO-004"
    $rs.Fields("ملاحظات").Value = "شراء زبدة طبيعية عالية الجودة"
    $rs.Fields("المستخدم").Value = "مدير المشتريات"
    $rs.Update()

    # شراء سكر ناعم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 5
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "وارد شراء"
    $rs.Fields("الكمية").Value = 200.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 12.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PO-005"
    $rs.Fields("ملاحظات").Value = "شراء سكر أبيض ناعم"
    $rs.Fields("المستخدم").Value = "مدير المشتريات"
    $rs.Update()

    # شراء علب كرتونية
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 8
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "وارد شراء"
    $rs.Fields("الكمية").Value = 2000.0
    $rs.Fields("وحدة_القياس").Value = 3
    $rs.Fields("تكلفة_الوحدة").Value = 2.50
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PO-006"
    $rs.Fields("ملاحظات").Value = "شراء علب كرتونية للتعبئة"
    $rs.Fields("المستخدم").Value = "مدير المشتريات"
    $rs.Update()
    
    $rs.Close()
    Write-Host "تم تسجيل حركات الشراء" -ForegroundColor Green
    
    Write-Host "=== الخطوة 3: بدء عملية الإنتاج ===" -ForegroundColor Yellow
    Write-Host "إنتاج 100 قطعة من معمول الجوز والعسل حسب الوصفة رقم 1" -ForegroundColor Cyan
    
    # صرف المواد الخام للإنتاج
    $rs = $db.OpenRecordset("حركات_المخزون")
    
    # صرف دقيق القمح - 30 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 1
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "صادر إنتاج"
    $rs.Fields("الكمية").Value = 30.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 8.50
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PROD-001"
    $rs.Fields("ملاحظات").Value = "صرف للإنتاج - دفعة معمول الجوز والعسل"
    $rs.Fields("المستخدم").Value = "مدير الإنتاج"
    $rs.Update()

    # صرف جوز مقشر - 8 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 2
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "صادر إنتاج"
    $rs.Fields("الكمية").Value = 8.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 45.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PROD-001"
    $rs.Fields("ملاحظات").Value = "صرف للإنتاج - دفعة معمول الجوز والعسل"
    $rs.Fields("المستخدم").Value = "مدير الإنتاج"
    $rs.Update()

    # صرف عسل نحل - 5 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 3
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "صادر إنتاج"
    $rs.Fields("الكمية").Value = 5.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 80.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PROD-001"
    $rs.Fields("ملاحظات").Value = "صرف للإنتاج - دفعة معمول الجوز والعسل"
    $rs.Fields("المستخدم").Value = "مدير الإنتاج"
    $rs.Update()

    # صرف زبدة طبيعية - 6 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 4
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "صادر إنتاج"
    $rs.Fields("الكمية").Value = 6.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 25.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PROD-001"
    $rs.Fields("ملاحظات").Value = "صرف للإنتاج - دفعة معمول الجوز والعسل"
    $rs.Fields("المستخدم").Value = "مدير الإنتاج"
    $rs.Update()

    # صرف سكر ناعم - 4 كجم
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 5
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "صادر إنتاج"
    $rs.Fields("الكمية").Value = 4.0
    $rs.Fields("وحدة_القياس").Value = 1
    $rs.Fields("تكلفة_الوحدة").Value = 12.00
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PROD-001"
    $rs.Fields("ملاحظات").Value = "صرف للإنتاج - دفعة معمول الجوز والعسل"
    $rs.Fields("المستخدم").Value = "مدير الإنتاج"
    $rs.Update()

    # صرف علب كرتونية - 100 قطعة
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 8
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "صادر إنتاج"
    $rs.Fields("الكمية").Value = 100.0
    $rs.Fields("وحدة_القياس").Value = 3
    $rs.Fields("تكلفة_الوحدة").Value = 2.50
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PROD-001"
    $rs.Fields("ملاحظات").Value = "صرف للتعبئة - دفعة معمول الجوز والعسل"
    $rs.Fields("المستخدم").Value = "مدير الإنتاج"
    $rs.Update()
    
    $rs.Close()
    Write-Host "تم صرف المواد الخام للإنتاج" -ForegroundColor Green

    Write-Host "=== الخطوة 4: إنتاج المنتج النهائي ===" -ForegroundColor Yellow

    # إضافة المنتج النهائي للمخزون
    $rs = $db.OpenRecordset("حركات_المخزون")

    # إنتاج 100 قطعة معمول الجوز والعسل
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 9 # معمول الجوز والعسل
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "وارد إنتاج"
    $rs.Fields("الكمية").Value = 100.0
    $rs.Fields("وحدة_القياس").Value = 3 # قطعة
    $rs.Fields("تكلفة_الوحدة").Value = 24.6465 # التكلفة المحسوبة
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "PROD-001"
    $rs.Fields("ملاحظات").Value = "إنتاج 100 قطعة معمول الجوز والعسل 600 جرام"
    $rs.Fields("المستخدم").Value = "مدير الإنتاج"
    $rs.Update()

    $rs.Close()

    # إضافة رصيد المنتج النهائي
    $rs = $db.OpenRecordset("أرصدة_المخزون")
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 9
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("الكمية_المتاحة").Value = 100.0
    $rs.Fields("الكمية_المحجوزة").Value = 0.0
    $rs.Fields("متوسط_التكلفة").Value = 24.6465
    $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
    $rs.Update()
    $rs.Close()

    Write-Host "تم إنتاج 100 قطعة من معمول الجوز والعسل" -ForegroundColor Green

    Write-Host "=== الخطوة 5: تحديث أرصدة المخزون ===" -ForegroundColor Yellow

    # تحديث أرصدة المواد الخام بعد الصرف
    $rs = $db.OpenRecordset("SELECT * FROM أرصدة_المخزون WHERE رقم_المادة = 1") # دقيق
    if (-not $rs.EOF) {
        $rs.Edit()
        $rs.Fields("الكمية_المتاحة").Value = 470.0 # 500 - 30
        $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
        $rs.Update()
    }
    $rs.Close()

    $rs = $db.OpenRecordset("SELECT * FROM أرصدة_المخزون WHERE رقم_المادة = 2") # جوز
    if (-not $rs.EOF) {
        $rs.Edit()
        $rs.Fields("الكمية_المتاحة").Value = 92.0 # 100 - 8
        $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
        $rs.Update()
    }
    $rs.Close()

    $rs = $db.OpenRecordset("SELECT * FROM أرصدة_المخزون WHERE رقم_المادة = 3") # عسل
    if (-not $rs.EOF) {
        $rs.Edit()
        $rs.Fields("الكمية_المتاحة").Value = 45.0 # 50 - 5
        $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
        $rs.Update()
    }
    $rs.Close()

    $rs = $db.OpenRecordset("SELECT * FROM أرصدة_المخزون WHERE رقم_المادة = 4") # زبدة
    if (-not $rs.EOF) {
        $rs.Edit()
        $rs.Fields("الكمية_المتاحة").Value = 74.0 # 80 - 6
        $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
        $rs.Update()
    }
    $rs.Close()

    $rs = $db.OpenRecordset("SELECT * FROM أرصدة_المخزون WHERE رقم_المادة = 5") # سكر
    if (-not $rs.EOF) {
        $rs.Edit()
        $rs.Fields("الكمية_المتاحة").Value = 196.0 # 200 - 4
        $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
        $rs.Update()
    }
    $rs.Close()

    $rs = $db.OpenRecordset("SELECT * FROM أرصدة_المخزون WHERE رقم_المادة = 8") # علب
    if (-not $rs.EOF) {
        $rs.Edit()
        $rs.Fields("الكمية_المتاحة").Value = 1900.0 # 2000 - 100
        $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
        $rs.Update()
    }
    $rs.Close()

    Write-Host "تم تحديث أرصدة المخزون" -ForegroundColor Green

    Write-Host "=== الخطوة 6: بيع جزء من الإنتاج ===" -ForegroundColor Yellow

    # بيع 50 قطعة من المنتج النهائي
    $rs = $db.OpenRecordset("حركات_المخزون")
    $rs.AddNew()
    $rs.Fields("رقم_المادة").Value = 9
    $rs.Fields("رقم_المستودع").Value = 1
    $rs.Fields("نوع_الحركة").Value = "صادر بيع"
    $rs.Fields("الكمية").Value = 50.0
    $rs.Fields("وحدة_القياس").Value = 3
    $rs.Fields("تكلفة_الوحدة").Value = 24.6465
    $rs.Fields("تاريخ_الحركة").Value = Get-Date
    $rs.Fields("رقم_المرجع").Value = "SALE-001"
    $rs.Fields("ملاحظات").Value = "بيع 50 قطعة معمول الجوز والعسل - سعر البيع 85 جنيه للقطعة"
    $rs.Fields("المستخدم").Value = "مدير المبيعات"
    $rs.Update()
    $rs.Close()

    # تحديث رصيد المنتج النهائي
    $rs = $db.OpenRecordset("SELECT * FROM أرصدة_المخزون WHERE رقم_المادة = 9")
    if (-not $rs.EOF) {
        $rs.Edit()
        $rs.Fields("الكمية_المتاحة").Value = 50.0 # 100 - 50
        $rs.Fields("تاريخ_آخر_تحديث").Value = Get-Date
        $rs.Update()
    }
    $rs.Close()

    Write-Host "تم بيع 50 قطعة من المنتج النهائي" -ForegroundColor Green

    Write-Host "=== ملخص المثال التطبيقي ===" -ForegroundColor Yellow
    Write-Host "✓ تم شراء المواد الخام وإضافتها للمخزون" -ForegroundColor Green
    Write-Host "✓ تم صرف المواد الخام للإنتاج حسب الوصفة" -ForegroundColor Green
    Write-Host "✓ تم إنتاج 100 قطعة من معمول الجوز والعسل" -ForegroundColor Green
    Write-Host "✓ تم تحديث أرصدة المخزون تلقائياً" -ForegroundColor Green
    Write-Host "✓ تم بيع 50 قطعة من الإنتاج" -ForegroundColor Green
    Write-Host "✓ الرصيد المتبقي: 50 قطعة في المخزون" -ForegroundColor Green

    Write-Host "=== تحليل الربحية ===" -ForegroundColor Cyan
    $تكلفة_الإنتاج_الإجمالية = 100 * 24.6465
    $إيرادات_البيع = 50 * 85.00
    $تكلفة_المبيعات = 50 * 24.6465
    $إجمالي_الربح = $إيرادات_البيع - $تكلفة_المبيعات
    $نسبة_الربح = ($إجمالي_الربح / $تكلفة_المبيعات) * 100

    Write-Host "تكلفة الإنتاج الإجمالية: $([math]::Round($تكلفة_الإنتاج_الإجمالية, 2)) جنيه" -ForegroundColor White
    Write-Host "إيرادات البيع: $([math]::Round($إيرادات_البيع, 2)) جنيه" -ForegroundColor White
    Write-Host "تكلفة المبيعات: $([math]::Round($تكلفة_المبيعات, 2)) جنيه" -ForegroundColor White
    Write-Host "إجمالي الربح: $([math]::Round($إجمالي_الربح, 2)) جنيه" -ForegroundColor Green
    Write-Host "نسبة الربح: $([math]::Round($نسبة_الربح, 2))%" -ForegroundColor Green

    Write-Host "تم إكمال المثال التطبيقي بنجاح!" -ForegroundColor Green

    # إغلاق قاعدة البيانات
    $db.Close()

} catch {
    Write-Host "حدث خطأ: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($rs) { try { $rs.Close() } catch {} }
    if ($db) { try { $db.Close() } catch {} }
    if ($dao) {
        try {
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dao) | Out-Null
        } catch {}
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}
