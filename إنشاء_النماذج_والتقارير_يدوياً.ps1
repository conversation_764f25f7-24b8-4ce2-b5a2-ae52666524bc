# سكريبت إنشاء النماذج والتقارير يدوياً
# Script to Create Forms and Reports Manually

Write-Host "===============================================" -ForegroundColor Magenta
Write-Host "        إنشاء النماذج والتقارير يدوياً        " -ForegroundColor Magenta
Write-Host "===============================================" -ForegroundColor Magenta
Write-Host ""

# إغلاق أي عمليات Access مفتوحة
Write-Host "إغلاق أي عمليات Access مفتوحة..." -ForegroundColor Yellow
Get-Process -Name "MSACCESS" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 3

$dbPath = "نظام_إدارة_تصنيع_المعمول_جديد.accdb"

if (-not (Test-Path $dbPath)) {
    Write-Host "خطأ: قاعدة البيانات غير موجودة." -ForegroundColor Red
    exit
}

try {
    Write-Host "فتح Access لإنشاء النماذج والتقارير..." -ForegroundColor Green
    
    # فتح Access مع قاعدة البيانات
    $access = New-Object -ComObject Access.Application
    $access.Visible = $true  # جعل Access مرئي للمستخدم
    $access.OpenCurrentDatabase($dbPath)
    
    Write-Host "تم فتح Access بنجاح. سيتم إنشاء النماذج والتقارير..." -ForegroundColor Green
    
    # انتظار قليل للتأكد من فتح قاعدة البيانات
    Start-Sleep -Seconds 3
    
    Write-Host "إنشاء النماذج الأساسية..." -ForegroundColor Cyan
    
    # إنشاء النماذج باستخدام DoCmd
    try {
        # 1. نموذج إدارة المواد والمنتجات
        $access.DoCmd.NewForm(0, "المواد_والمنتجات", "", "", 1, "نموذج_إدارة_المواد")
        $access.DoCmd.Close(2, "نموذج_إدارة_المواد", 1)
        Write-Host "✓ تم إنشاء نموذج إدارة المواد" -ForegroundColor Green
        Start-Sleep -Seconds 1
    } catch {
        Write-Host "تحذير: مشكلة في إنشاء نموذج إدارة المواد: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    try {
        # 2. نموذج إدارة المستودعات
        $access.DoCmd.NewForm(0, "المستودعات", "", "", 1, "نموذج_إدارة_المستودعات")
        $access.DoCmd.Close(2, "نموذج_إدارة_المستودعات", 1)
        Write-Host "✓ تم إنشاء نموذج إدارة المستودعات" -ForegroundColor Green
        Start-Sleep -Seconds 1
    } catch {
        Write-Host "تحذير: مشكلة في إنشاء نموذج إدارة المستودعات: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    try {
        # 3. نموذج إدارة الموردين
        $access.DoCmd.NewForm(0, "الموردين", "", "", 1, "نموذج_إدارة_الموردين")
        $access.DoCmd.Close(2, "نموذج_إدارة_الموردين", 1)
        Write-Host "✓ تم إنشاء نموذج إدارة الموردين" -ForegroundColor Green
        Start-Sleep -Seconds 1
    } catch {
        Write-Host "تحذير: مشكلة في إنشاء نموذج إدارة الموردين: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    try {
        # 4. نموذج إدارة العملاء
        $access.DoCmd.NewForm(0, "العملاء", "", "", 1, "نموذج_إدارة_العملاء")
        $access.DoCmd.Close(2, "نموذج_إدارة_العملاء", 1)
        Write-Host "✓ تم إنشاء نموذج إدارة العملاء" -ForegroundColor Green
        Start-Sleep -Seconds 1
    } catch {
        Write-Host "تحذير: مشكلة في إنشاء نموذج إدارة العملاء: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    try {
        # 5. نموذج إدارة الوصفات
        $access.DoCmd.NewForm(0, "الوصفات", "", "", 1, "نموذج_إدارة_الوصفات")
        $access.DoCmd.Close(2, "نموذج_إدارة_الوصفات", 1)
        Write-Host "✓ تم إنشاء نموذج إدارة الوصفات" -ForegroundColor Green
        Start-Sleep -Seconds 1
    } catch {
        Write-Host "تحذير: مشكلة في إنشاء نموذج إدارة الوصفات: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    try {
        # 6. نموذج أرصدة المخزون
        $access.DoCmd.NewForm(2, "استعلام_أرصدة_المخزون", "", "", 1, "نموذج_أرصدة_المخزون")
        $access.DoCmd.Close(2, "نموذج_أرصدة_المخزون", 1)
        Write-Host "✓ تم إنشاء نموذج أرصدة المخزون" -ForegroundColor Green
        Start-Sleep -Seconds 1
    } catch {
        Write-Host "تحذير: مشكلة في إنشاء نموذج أرصدة المخزون: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "إنشاء التقارير الأساسية..." -ForegroundColor Cyan
    
    # إنشاء التقارير باستخدام DoCmd
    try {
        # 1. تقرير أرصدة المخزون
        $access.DoCmd.NewReport(0, "استعلام_أرصدة_المخزون", "", "", 1, "تقرير_أرصدة_المخزون")
        $access.DoCmd.Close(3, "تقرير_أرصدة_المخزون", 1)
        Write-Host "✓ تم إنشاء تقرير أرصدة المخزون" -ForegroundColor Green
        Start-Sleep -Seconds 1
    } catch {
        Write-Host "تحذير: مشكلة في إنشاء تقرير أرصدة المخزون: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    try {
        # 2. تقرير المواد تحت الحد الأدنى
        $access.DoCmd.NewReport(0, "استعلام_المواد_تحت_الحد_الأدنى", "", "", 1, "تقرير_المواد_تحت_الحد_الأدنى")
        $access.DoCmd.Close(3, "تقرير_المواد_تحت_الحد_الأدنى", 1)
        Write-Host "✓ تم إنشاء تقرير المواد تحت الحد الأدنى" -ForegroundColor Green
        Start-Sleep -Seconds 1
    } catch {
        Write-Host "تحذير: مشكلة في إنشاء تقرير المواد تحت الحد الأدنى: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    try {
        # 3. تقرير تفاصيل الوصفة
        $access.DoCmd.NewReport(0, "استعلام_تفاصيل_الوصفة", "", "", 1, "تقرير_تفاصيل_الوصفة")
        $access.DoCmd.Close(3, "تقرير_تفاصيل_الوصفة", 1)
        Write-Host "✓ تم إنشاء تقرير تفاصيل الوصفة" -ForegroundColor Green
        Start-Sleep -Seconds 1
    } catch {
        Write-Host "تحذير: مشكلة في إنشاء تقرير تفاصيل الوصفة: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    try {
        # 4. تقرير تكلفة الوصفة
        $access.DoCmd.NewReport(0, "استعلام_تكلفة_الوصفة", "", "", 1, "تقرير_تكلفة_الوصفة")
        $access.DoCmd.Close(3, "تقرير_تكلفة_الوصفة", 1)
        Write-Host "✓ تم إنشاء تقرير تكلفة الوصفة" -ForegroundColor Green
        Start-Sleep -Seconds 1
    } catch {
        Write-Host "تحذير: مشكلة في إنشاء تقرير تكلفة الوصفة: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    try {
        # 5. تقرير قائمة المواد
        $access.DoCmd.NewReport(0, "استعلام_قائمة_المواد", "", "", 1, "تقرير_قائمة_المواد")
        $access.DoCmd.Close(3, "تقرير_قائمة_المواد", 1)
        Write-Host "✓ تم إنشاء تقرير قائمة المواد" -ForegroundColor Green
        Start-Sleep -Seconds 1
    } catch {
        Write-Host "تحذير: مشكلة في إنشاء تقرير قائمة المواد: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    try {
        # 6. تقرير تحليل الربحية
        $access.DoCmd.NewReport(0, "استعلام_تحليل_الربحية", "", "", 1, "تقرير_تحليل_الربحية")
        $access.DoCmd.Close(3, "تقرير_تحليل_الربحية", 1)
        Write-Host "✓ تم إنشاء تقرير تحليل الربحية" -ForegroundColor Green
        Start-Sleep -Seconds 1
    } catch {
        Write-Host "تحذير: مشكلة في إنشاء تقرير تحليل الربحية: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    try {
        # 7. تقرير قيمة المخزون
        $access.DoCmd.NewReport(0, "استعلام_قيمة_المخزون", "", "", 1, "تقرير_قيمة_المخزون")
        $access.DoCmd.Close(3, "تقرير_قيمة_المخزون", 1)
        Write-Host "✓ تم إنشاء تقرير قيمة المخزون" -ForegroundColor Green
        Start-Sleep -Seconds 1
    } catch {
        Write-Host "تحذير: مشكلة في إنشاء تقرير قيمة المخزون: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host "    تم إنجاز النماذج والتقارير بنجاح!        " -ForegroundColor Green
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "📝 النماذج المنشأة:" -ForegroundColor Cyan
    Write-Host "• نموذج إدارة المواد" -ForegroundColor White
    Write-Host "• نموذج إدارة المستودعات" -ForegroundColor White
    Write-Host "• نموذج إدارة الموردين" -ForegroundColor White
    Write-Host "• نموذج إدارة العملاء" -ForegroundColor White
    Write-Host "• نموذج إدارة الوصفات" -ForegroundColor White
    Write-Host "• نموذج أرصدة المخزون" -ForegroundColor White
    Write-Host ""
    
    Write-Host "📋 التقارير المنشأة:" -ForegroundColor Cyan
    Write-Host "• تقرير أرصدة المخزون" -ForegroundColor White
    Write-Host "• تقرير المواد تحت الحد الأدنى" -ForegroundColor White
    Write-Host "• تقرير تفاصيل الوصفة" -ForegroundColor White
    Write-Host "• تقرير تكلفة الوصفة" -ForegroundColor White
    Write-Host "• تقرير قائمة المواد" -ForegroundColor White
    Write-Host "• تقرير تحليل الربحية" -ForegroundColor White
    Write-Host "• تقرير قيمة المخزون" -ForegroundColor White
    Write-Host ""
    
    Write-Host "🎯 للاستخدام:" -ForegroundColor Cyan
    Write-Host "• استخدم النماذج لإدخال وتعديل البيانات" -ForegroundColor White
    Write-Host "• استخدم التقارير للطباعة والعرض" -ForegroundColor White
    Write-Host "• يمكنك تخصيص النماذج والتقارير حسب الحاجة" -ForegroundColor White
    Write-Host ""
    
    Write-Host "Access سيبقى مفتوحاً لتتمكن من مراجعة النماذج والتقارير..." -ForegroundColor Yellow
    Write-Host "يمكنك إغلاق هذه النافذة والعمل في Access مباشرة." -ForegroundColor Yellow
    
} catch {
    Write-Host "خطأ في إنشاء النماذج والتقارير: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 حلول بديلة:" -ForegroundColor Yellow
    Write-Host "1. افتح قاعدة البيانات في Access يدوياً" -ForegroundColor White
    Write-Host "2. استخدم معالج النماذج (Form Wizard) لإنشاء النماذج" -ForegroundColor White
    Write-Host "3. استخدم معالج التقارير (Report Wizard) لإنشاء التقارير" -ForegroundColor White
    Write-Host "4. اختر الاستعلامات كمصدر للبيانات" -ForegroundColor White
} finally {
    # لا نغلق Access هنا لنترك المستخدم يعمل عليه
    Write-Host ""
    Write-Host "ملاحظة: تم ترك Access مفتوحاً لتتمكن من العمل على النماذج والتقارير." -ForegroundColor Cyan
}

Write-Host ""
Write-Host "===============================================" -ForegroundColor Magenta
Write-Host "    النماذج والتقارير جاهزة للاستخدام!       " -ForegroundColor Magenta
Write-Host "===============================================" -ForegroundColor Magenta
