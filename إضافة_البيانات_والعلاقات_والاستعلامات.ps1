# سكريبت إضافة البيانات والعلاقات والاستعلامات لنظام إدارة تصنيع المعمول
# Adding Data, Relationships, and Queries to Ma'amoul Manufacturing System

Write-Host "بدء إضافة البيانات والعلاقات والاستعلامات..." -ForegroundColor Green

$dbPath = "نظام_إدارة_تصنيع_المعمول.accdb"

if (-not (Test-Path $dbPath)) {
    Write-Host "خطأ: قاعدة البيانات غير موجودة. يرجى تشغيل سكريبت إنشاء النظام أولاً." -ForegroundColor Red
    exit
}

try {
    # فتح قاعدة البيانات
    $dao = New-Object -ComObject DAO.DBEngine.120
    $db = $dao.OpenDatabase($dbPath)
    
    Write-Host "إضافة البيانات الأساسية..." -ForegroundColor Cyan
    
    # إضافة المستودعات
    $rs = $db.OpenRecordset("المستودعات")
    $rs.AddNew()
    $rs.Fields("اسم_المستودع").Value = "مستودع المواد الخام"
    $rs.Fields("الموقع").Value = "الطابق الأرضي - قسم أ"
    $rs.Fields("المسؤول").Value = "أحمد محمد"
    $rs.Fields("الهاتف").Value = "01234567890"
    $rs.Update()
    
    $rs.AddNew()
    $rs.Fields("اسم_المستودع").Value = "مستودع المنتجات النهائية"
    $rs.Fields("الموقع").Value = "الطابق الأول - قسم ب"
    $rs.Fields("المسؤول").Value = "فاطمة علي"
    $rs.Fields("الهاتف").Value = "01098765432"
    $rs.Update()
    
    $rs.AddNew()
    $rs.Fields("اسم_المستودع").Value = "مستودع التعبئة والتغليف"
    $rs.Fields("الموقع").Value = "الطابق الأرضي - قسم ج"
    $rs.Fields("المسؤول").Value = "محمد حسن"
    $rs.Fields("الهاتف").Value = "01156789012"
    $rs.Update()
    
    $rs.Close()
    Write-Host "تم إضافة المستودعات" -ForegroundColor Green
    
    # إضافة فئات المواد
    $rs = $db.OpenRecordset("فئات_المواد")
    $categories = @(
        @{Name="مكسرات"; Desc="جميع أنواع المكسرات المستخدمة في الإنتاج"},
        @{Name="دقيق ونشويات"; Desc="الدقيق والنشا والمواد النشوية"},
        @{Name="سكريات ومحليات"; Desc="السكر والعسل والمحليات الطبيعية"},
        @{Name="زيوت ودهون"; Desc="الزيوت والزبدة والدهون المستخدمة"},
        @{Name="توابل ونكهات"; Desc="التوابل والنكهات والمواد المحسنة"},
        @{Name="مواد تعبئة وتغليف"; Desc="العلب والأكياس ومواد التغليف"},
        @{Name="منتجات نهائية"; Desc="المنتجات النهائية الجاهزة للبيع"}
    )
    
    foreach ($cat in $categories) {
        $rs.AddNew()
        $rs.Fields("اسم_الفئة").Value = $cat.Name
        $rs.Fields("الوصف").Value = $cat.Desc
        $rs.Update()
    }
    $rs.Close()
    Write-Host "تم إضافة فئات المواد" -ForegroundColor Green
    
    # إضافة الموردين
    $rs = $db.OpenRecordset("الموردين")
    $suppliers = @(
        @{Name="شركة المكسرات المصرية"; Address="القاهرة - مدينة نصر"; Phone="01234567890"; Email="<EMAIL>"; Contact="أحمد محمود"; Terms="نقدي عند التسليم"},
        @{Name="مطاحن الدقيق الحديثة"; Address="الجيزة - 6 أكتوبر"; Phone="01098765432"; Email="<EMAIL>"; Contact="فاطمة علي"; Terms="30 يوم"},
        @{Name="مناحل العسل الطبيعي"; Address="الفيوم - سنورس"; Phone="01156789012"; Email="<EMAIL>"; Contact="محمد حسن"; Terms="نقدي عند التسليم"},
        @{Name="شركة الزيوت النباتية"; Address="الإسكندرية - برج العرب"; Phone="01223456789"; Email="<EMAIL>"; Contact="سارة أحمد"; Terms="15 يوم"},
        @{Name="مصنع التعبئة والتغليف"; Address="القليوبية - شبرا الخيمة"; Phone="01087654321"; Email="<EMAIL>"; Contact="خالد عبدالله"; Terms="45 يوم"}
    )
    
    foreach ($sup in $suppliers) {
        $rs.AddNew()
        $rs.Fields("اسم_المورد").Value = $sup.Name
        $rs.Fields("العنوان").Value = $sup.Address
        $rs.Fields("الهاتف").Value = $sup.Phone
        $rs.Fields("البريد_الإلكتروني").Value = $sup.Email
        $rs.Fields("شخص_الاتصال").Value = $sup.Contact
        $rs.Fields("شروط_الدفع").Value = $sup.Terms
        $rs.Update()
    }
    $rs.Close()
    Write-Host "تم إضافة الموردين" -ForegroundColor Green
    
    # إضافة العملاء
    $rs = $db.OpenRecordset("العملاء")
    $customers = @(
        @{Name="سوبر ماركت الأهرام"; Address="القاهرة - مصر الجديدة"; Phone="0227654321"; Email="<EMAIL>"; Contact="نادر محمد"; Credit=50000},
        @{Name="هايبر ماركت كارفور"; Address="الجيزة - المهندسين"; Phone="0233456789"; Email="<EMAIL>"; Contact="مريم أحمد"; Credit=100000},
        @{Name="متاجر سبينيس"; Address="القاهرة - التجمع الخامس"; Phone="0226789012"; Email="<EMAIL>"; Contact="أمير سالم"; Credit=75000},
        @{Name="محلات العثيم"; Address="الإسكندرية - سموحة"; Phone="0334567890"; Email="<EMAIL>"; Contact="ليلى حسن"; Credit=60000},
        @{Name="بيع بالتجزئة مباشر"; Address="متنوع"; Phone="01012345678"; Email="<EMAIL>"; Contact="مبيعات مباشرة"; Credit=10000}
    )
    
    foreach ($cust in $customers) {
        $rs.AddNew()
        $rs.Fields("اسم_العميل").Value = $cust.Name
        $rs.Fields("العنوان").Value = $cust.Address
        $rs.Fields("الهاتف").Value = $cust.Phone
        $rs.Fields("البريد_الإلكتروني").Value = $cust.Email
        $rs.Fields("شخص_الاتصال").Value = $cust.Contact
        $rs.Fields("حد_الائتمان").Value = $cust.Credit
        $rs.Update()
    }
    $rs.Close()
    Write-Host "تم إضافة العملاء" -ForegroundColor Green
    
    # إضافة المواد والمنتجات
    $rs = $db.OpenRecordset("المواد_والمنتجات")
    $materials = @(
        @{Code="NUT001"; Name="جوز مقشر درجة أولى"; Category=1; Unit=1; Cost=180.00; MinStock=50; MaxStock=500; ReorderPoint=100; ShelfLife=365; Type="مادة خام"},
        @{Code="FLR001"; Name="دقيق أبيض فاخر"; Category=2; Unit=1; Cost=12.50; MinStock=100; MaxStock=1000; ReorderPoint=200; ShelfLife=180; Type="مادة خام"},
        @{Code="HON001"; Name="عسل نحل طبيعي"; Category=3; Unit=1; Cost=85.00; MinStock=20; MaxStock=200; ReorderPoint=50; ShelfLife=730; Type="مادة خام"},
        @{Code="BUT001"; Name="زبدة طبيعية"; Category=4; Unit=1; Cost=45.00; MinStock=30; MaxStock=300; ReorderPoint=75; ShelfLife=90; Type="مادة خام"},
        @{Code="SUG001"; Name="سكر أبيض ناعم"; Category=3; Unit=1; Cost=18.00; MinStock=50; MaxStock=500; ReorderPoint=125; ShelfLife=365; Type="مادة خام"},
        @{Code="CIN001"; Name="قرفة مطحونة"; Category=5; Unit=2; Cost=120.00; MinStock=5; MaxStock=50; ReorderPoint=15; ShelfLife=365; Type="مادة خام"},
        @{Code="VAN001"; Name="فانيليا سائلة"; Category=5; Unit=5; Cost=25.00; MinStock=10; MaxStock=100; ReorderPoint=25; ShelfLife=730; Type="مادة خام"},
        @{Code="BOX001"; Name="علبة كرتون 600 جرام"; Category=6; Unit=3; Cost=2.50; MinStock=500; MaxStock=5000; ReorderPoint=1000; ShelfLife=1095; Type="مادة تعبئة"},
        @{Code="LAB001"; Name="ملصق المنتج"; Category=6; Unit=3; Cost=0.15; MinStock=1000; MaxStock=10000; ReorderPoint=2000; ShelfLife=1095; Type="مادة تعبئة"},
        @{Code="PRD001"; Name="معمول الجوز والعسل 600 جرام"; Category=7; Unit=4; Cost=24.65; SellPrice=85.00; MinStock=50; MaxStock=1000; ReorderPoint=100; ShelfLife=45; Type="منتج نهائي"}
    )
    
    foreach ($mat in $materials) {
        $rs.AddNew()
        $rs.Fields("كود_المادة").Value = $mat.Code
        $rs.Fields("اسم_المادة").Value = $mat.Name
        $rs.Fields("رقم_الفئة").Value = $mat.Category
        $rs.Fields("وحدة_القياس_الأساسية").Value = $mat.Unit
        $rs.Fields("التكلفة_المعيارية").Value = $mat.Cost
        if ($mat.SellPrice) { $rs.Fields("سعر_البيع").Value = $mat.SellPrice }
        $rs.Fields("الحد_الأدنى_للمخزون").Value = $mat.MinStock
        $rs.Fields("الحد_الأقصى_للمخزون").Value = $mat.MaxStock
        $rs.Fields("نقطة_إعادة_الطلب").Value = $mat.ReorderPoint
        $rs.Fields("مدة_الصلاحية_بالأيام").Value = $mat.ShelfLife
        $rs.Fields("نوع_المادة").Value = $mat.Type
        $rs.Update()
    }
    $rs.Close()
    Write-Host "تم إضافة المواد والمنتجات" -ForegroundColor Green
    
    Write-Host "تم إضافة جميع البيانات الأساسية بنجاح!" -ForegroundColor Green
    
} catch {
    Write-Host "خطأ في إضافة البيانات: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # تنظيف الكائنات
    if ($rs) { $rs.Close() }
    if ($db) { $db.Close() }
    if ($dao) { [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dao) | Out-Null }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}

Write-Host "انتهى إضافة البيانات. الخطوة التالية: إنشاء الاستعلامات والنماذج والتقارير..." -ForegroundColor Yellow
