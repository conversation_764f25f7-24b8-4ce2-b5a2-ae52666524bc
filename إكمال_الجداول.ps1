# سكريپت إكمال الجداول المتبقية
# تاريخ الإنشاء: 2025-09-19

Write-Host "بدء إكمال الجداول المتبقية..." -ForegroundColor Green

try {
    $dbPath = Join-<PERSON> (Get-Location) "نظام_إدارة_تصنيع_المعمول.accdb"
    
    if (-not (Test-Path $dbPath)) {
        Write-Host "قاعدة البيانات غير موجودة!" -ForegroundColor Red
        exit 1
    }
    
    # فتح قاعدة البيانات
    $dao = New-Object -ComObject DAO.DBEngine.120
    $db = $dao.OpenDatabase($dbPath)
    
    Write-Host "تم فتح قاعدة البيانات بنجاح" -ForegroundColor Green
    
    # جدول فئات المواد
    Write-Host "إنشاء جدول فئات المواد..." -ForegroundColor Cyan
    $tabledef = $db.CreateTableDef("فئات_المواد")
    
    $field = $tabledef.CreateField("رقم_الفئة", 4) # dbLong
    $field.Attributes = 16 # dbAutoIncrField
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("اسم_الفئة", 10, 100) # dbText
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("وصف_الفئة", 12) # dbMemo
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("فئة_رئيسية", 4) # dbLong
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("رمز_الفئة", 10, 20) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("ملاحظات", 12) # dbMemo
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_الإنشاء", 8) # dbDate
    $field.DefaultValue = "Now()"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("نشط", 1) # dbBoolean
    $field.DefaultValue = "True"
    $tabledef.Fields.Append($field)
    
    # المفتاح الأساسي
    $index = $tabledef.CreateIndex("PrimaryKey")
    $index.Primary = $true
    $indexfield = $index.CreateField("رقم_الفئة")
    $index.Fields.Append($indexfield)
    $tabledef.Indexes.Append($index)
    
    $db.TableDefs.Append($tabledef)
    Write-Host "تم إنشاء جدول فئات المواد" -ForegroundColor Green
    
    # جدول المواد والمنتجات
    Write-Host "إنشاء جدول المواد والمنتجات..." -ForegroundColor Cyan
    $tabledef = $db.CreateTableDef("المواد_والمنتجات")
    
    $field = $tabledef.CreateField("رقم_المادة", 4) # dbLong
    $field.Attributes = 16 # dbAutoIncrField
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("كود_المادة", 10, 50) # dbText
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("اسم_المادة", 10, 200) # dbText
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("وصف_المادة", 12) # dbMemo
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("رقم_الفئة", 4) # dbLong
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("نوع_المادة", 10, 50) # dbText
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("وحدة_القياس_الأساسية", 4) # dbLong
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("وحدة_الشراء", 4) # dbLong
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("وحدة_البيع", 4) # dbLong
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("وحدة_الإنتاج", 4) # dbLong
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("الحد_الأدنى_للمخزون", 7) # dbDouble
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("الحد_الأقصى_للمخزون", 7) # dbDouble
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("نقطة_إعادة_الطلب", 7) # dbDouble
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("مدة_الصلاحية_بالأيام", 4) # dbLong
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("يتطلب_تتبع_الدفعات", 1) # dbBoolean
    $field.DefaultValue = "False"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("يتطلب_تتبع_الصلاحية", 1) # dbBoolean
    $field.DefaultValue = "False"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("التكلفة_المعيارية", 5) # dbCurrency
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("سعر_البيع_المقترح", 5) # dbCurrency
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("معدل_الضريبة", 7) # dbDouble
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("ملاحظات", 12) # dbMemo
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_الإنشاء", 8) # dbDate
    $field.DefaultValue = "Now()"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_آخر_تحديث", 8) # dbDate
    $field.DefaultValue = "Now()"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("نشط", 1) # dbBoolean
    $field.DefaultValue = "True"
    $tabledef.Fields.Append($field)
    
    # المفتاح الأساسي
    $index = $tabledef.CreateIndex("PrimaryKey")
    $index.Primary = $true
    $indexfield = $index.CreateField("رقم_المادة")
    $index.Fields.Append($indexfield)
    $tabledef.Indexes.Append($index)
    
    # فهرس فريد لكود المادة
    $index = $tabledef.CreateIndex("كود_المادة_فريد")
    $index.Unique = $true
    $indexfield = $index.CreateField("كود_المادة")
    $index.Fields.Append($indexfield)
    $tabledef.Indexes.Append($index)
    
    $db.TableDefs.Append($tabledef)
    Write-Host "تم إنشاء جدول المواد والمنتجات" -ForegroundColor Green
    
    # جدول الموردين
    Write-Host "إنشاء جدول الموردين..." -ForegroundColor Cyan
    $tabledef = $db.CreateTableDef("الموردين")
    
    $field = $tabledef.CreateField("رقم_المورد", 4) # dbLong
    $field.Attributes = 16 # dbAutoIncrField
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("كود_المورد", 10, 50) # dbText
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("اسم_المورد", 10, 200) # dbText
    $field.Required = $true
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("اسم_جهة_الاتصال", 10, 100) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("العنوان", 12) # dbMemo
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("المدينة", 10, 100) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("المحافظة", 10, 100) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("الرمز_البريدي", 10, 20) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("الهاتف", 10, 50) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("الفاكس", 10, 50) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("البريد_الإلكتروني", 10, 100) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("الموقع_الإلكتروني", 10, 200) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("شروط_الدفع", 10, 100) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("مدة_التوريد_بالأيام", 4) # dbLong
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("حد_الائتمان", 5) # dbCurrency
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("معدل_الخصم", 7) # dbDouble
    $field.DefaultValue = "0"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تقييم_المورد", 10, 20) # dbText
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("ملاحظات", 12) # dbMemo
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_الإنشاء", 8) # dbDate
    $field.DefaultValue = "Now()"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("تاريخ_آخر_تحديث", 8) # dbDate
    $field.DefaultValue = "Now()"
    $tabledef.Fields.Append($field)
    
    $field = $tabledef.CreateField("نشط", 1) # dbBoolean
    $field.DefaultValue = "True"
    $tabledef.Fields.Append($field)
    
    # المفتاح الأساسي
    $index = $tabledef.CreateIndex("PrimaryKey")
    $index.Primary = $true
    $indexfield = $index.CreateField("رقم_المورد")
    $index.Fields.Append($indexfield)
    $tabledef.Indexes.Append($index)
    
    # فهرس فريد لكود المورد
    $index = $tabledef.CreateIndex("كود_المورد_فريد")
    $index.Unique = $true
    $indexfield = $index.CreateField("كود_المورد")
    $index.Fields.Append($indexfield)
    $tabledef.Indexes.Append($index)
    
    $db.TableDefs.Append($tabledef)
    Write-Host "تم إنشاء جدول الموردين" -ForegroundColor Green
    
    Write-Host "تم إكمال الجداول الأساسية بنجاح!" -ForegroundColor Green
    
    # إغلاق قاعدة البيانات
    $db.Close()
    
} catch {
    Write-Host "حدث خطأ: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($db) { try { $db.Close() } catch {} }
    if ($dao) {
        try {
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($dao) | Out-Null
        } catch {}
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}
